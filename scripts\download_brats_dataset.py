#!/usr/bin/env python3
"""
Download and setup BraTS 2020 dataset for Healthcare Multimodal Triage Agent.

This script downloads the BraTS 2020 dataset from Kaggle and organizes it
for training the PaliGemma 2 model.
"""

import os
import sys
import shutil
import logging
from pathlib import Path
from typing import List, Dict, Optional
import argparse

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from healthcare_triage.utils.config import load_config
from healthcare_triage.utils.logging_utils import setup_comprehensive_logging


class BraTSDatasetManager:
    """Manager for BraTS 2020 dataset download and organization."""
    
    def __init__(self, config_path: Optional[str] = None):
        """Initialize the dataset manager."""
        self.config = load_config(config_path)
        self.logger = setup_comprehensive_logging(self.config)
        self.project_root = Path(__file__).parent.parent
        
    def download_brats_dataset(self) -> str:
        """Download BraTS 2020 dataset using kagglehub."""
        self.logger.info("Downloading BraTS 2020 dataset from Kaggle...")
        
        try:
            import kagglehub
            
            # Download latest version
            path = kagglehub.dataset_download("awsaf49/brats20-dataset-training-validation")
            self.logger.info(f"Dataset downloaded to: {path}")
            return path
            
        except ImportError:
            self.logger.error("kagglehub not installed. Installing...")
            import subprocess
            subprocess.check_call([sys.executable, "-m", "pip", "install", "kagglehub"])
            
            # Try again after installation
            import kagglehub
            path = kagglehub.dataset_download("awsaf49/brats20-dataset-training-validation")
            self.logger.info(f"Dataset downloaded to: {path}")
            return path
            
        except Exception as e:
            self.logger.error(f"Failed to download dataset: {e}")
            raise
    
    def organize_dataset(self, download_path: str):
        """Organize the downloaded dataset into the project structure."""
        self.logger.info("Organizing BraTS dataset...")
        
        download_path = Path(download_path)
        target_data_dir = self.project_root / "data" / "mri"
        
        # Create target directories
        for split in ["train", "val", "test"]:
            for subdir in ["images", "labels"]:
                (target_data_dir / split / subdir).mkdir(parents=True, exist_ok=True)
        
        # Find all NIfTI files in the download
        nifti_files = list(download_path.rglob("*.nii.gz"))
        self.logger.info(f"Found {len(nifti_files)} NIfTI files")
        
        # Organize files by patient and modality
        patients = {}
        for file_path in nifti_files:
            # Extract patient ID and modality from filename
            filename = file_path.name
            
            # BraTS naming convention: BraTS20_Training_XXX_modality.nii.gz
            if "BraTS20" in filename:
                parts = filename.split("_")
                if len(parts) >= 3:
                    patient_id = "_".join(parts[:3])  # BraTS20_Training_XXX
                    modality = parts[3].split(".")[0] if len(parts) > 3 else "unknown"
                    
                    if patient_id not in patients:
                        patients[patient_id] = {}
                    patients[patient_id][modality] = file_path
        
        self.logger.info(f"Found {len(patients)} patients")
        
        # Split patients into train/val/test
        patient_list = list(patients.keys())
        n_patients = len(patient_list)
        
        # 70% train, 15% val, 15% test
        n_train = int(0.7 * n_patients)
        n_val = int(0.15 * n_patients)
        
        train_patients = patient_list[:n_train]
        val_patients = patient_list[n_train:n_train + n_val]
        test_patients = patient_list[n_train + n_val:]
        
        self.logger.info(f"Split: {len(train_patients)} train, {len(val_patients)} val, {len(test_patients)} test")
        
        # Copy files to organized structure
        splits = {
            "train": train_patients,
            "val": val_patients,
            "test": test_patients
        }
        
        for split_name, patient_ids in splits.items():
            self.logger.info(f"Processing {split_name} split...")
            
            for patient_id in patient_ids:
                patient_data = patients[patient_id]
                
                # Use T1ce (T1 contrast-enhanced) as the main image if available
                # Otherwise use T1, T2, or FLAIR
                image_modalities = ["t1ce", "t1", "t2", "flair"]
                image_file = None
                
                for modality in image_modalities:
                    if modality in patient_data:
                        image_file = patient_data[modality]
                        break
                
                # Use segmentation as label
                label_file = patient_data.get("seg")
                
                if image_file and label_file:
                    # Copy image file
                    target_image = target_data_dir / split_name / "images" / f"{patient_id}_image.nii.gz"
                    shutil.copy2(image_file, target_image)
                    
                    # Copy label file
                    target_label = target_data_dir / split_name / "labels" / f"{patient_id}_label.nii.gz"
                    shutil.copy2(label_file, target_label)
                    
                    self.logger.debug(f"Copied {patient_id} to {split_name}")
                else:
                    self.logger.warning(f"Missing files for patient {patient_id}")
        
        self.logger.info("Dataset organization completed")
        
        # Create dataset summary
        self.create_dataset_summary(target_data_dir)
    
    def create_dataset_summary(self, data_dir: Path):
        """Create a summary of the organized dataset."""
        summary = {
            "dataset_name": "BraTS 2020",
            "organization_date": str(Path(__file__).stat().st_mtime),
            "splits": {}
        }
        
        for split in ["train", "val", "test"]:
            split_dir = data_dir / split
            
            if split_dir.exists():
                image_files = list((split_dir / "images").glob("*.nii.gz"))
                label_files = list((split_dir / "labels").glob("*.nii.gz"))
                
                summary["splits"][split] = {
                    "num_images": len(image_files),
                    "num_labels": len(label_files),
                    "sample_files": [f.name for f in image_files[:3]]  # First 3 as examples
                }
        
        # Save summary
        import json
        summary_path = data_dir / "dataset_summary.json"
        with open(summary_path, 'w') as f:
            json.dump(summary, f, indent=2)
        
        self.logger.info(f"Dataset summary saved to {summary_path}")
        
        # Print summary
        print("\n📊 BRATS DATASET SUMMARY")
        print("=" * 50)
        for split, info in summary["splits"].items():
            print(f"{split.upper()}: {info['num_images']} images, {info['num_labels']} labels")
    
    def validate_dataset(self):
        """Validate the organized dataset."""
        self.logger.info("Validating organized dataset...")
        
        data_dir = self.project_root / "data" / "mri"
        issues = []
        
        for split in ["train", "val", "test"]:
            split_dir = data_dir / split
            
            if not split_dir.exists():
                issues.append(f"Missing {split} directory")
                continue
            
            image_dir = split_dir / "images"
            label_dir = split_dir / "labels"
            
            if not image_dir.exists():
                issues.append(f"Missing {split}/images directory")
            if not label_dir.exists():
                issues.append(f"Missing {split}/labels directory")
            
            # Check file counts match
            if image_dir.exists() and label_dir.exists():
                image_files = list(image_dir.glob("*.nii.gz"))
                label_files = list(label_dir.glob("*.nii.gz"))
                
                if len(image_files) != len(label_files):
                    issues.append(f"{split}: Image count ({len(image_files)}) != Label count ({len(label_files)})")
                
                # Check file pairing
                image_ids = {f.stem.replace("_image", "") for f in image_files}
                label_ids = {f.stem.replace("_label", "") for f in label_files}
                
                missing_labels = image_ids - label_ids
                missing_images = label_ids - image_ids
                
                if missing_labels:
                    issues.append(f"{split}: Missing labels for {len(missing_labels)} images")
                if missing_images:
                    issues.append(f"{split}: Missing images for {len(missing_images)} labels")
        
        if issues:
            self.logger.warning("Dataset validation issues found:")
            for issue in issues:
                self.logger.warning(f"  - {issue}")
            return False
        else:
            self.logger.info("Dataset validation passed ✅")
            return True
    
    def setup_complete_dataset(self):
        """Complete dataset setup process."""
        try:
            # Download dataset
            download_path = self.download_brats_dataset()
            
            # Organize dataset
            self.organize_dataset(download_path)
            
            # Validate dataset
            if self.validate_dataset():
                self.logger.info("✅ BraTS dataset setup completed successfully!")
                return True
            else:
                self.logger.error("❌ Dataset validation failed")
                return False
                
        except Exception as e:
            self.logger.error(f"Dataset setup failed: {e}")
            return False


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Download and setup BraTS 2020 dataset")
    parser.add_argument("--config", type=str, help="Path to configuration file")
    parser.add_argument("--validate-only", action="store_true", help="Only validate existing dataset")
    
    args = parser.parse_args()
    
    try:
        manager = BraTSDatasetManager(args.config)
        
        if args.validate_only:
            success = manager.validate_dataset()
        else:
            success = manager.setup_complete_dataset()
        
        if success:
            print("\n🎉 Dataset setup completed successfully!")
            print("\nNext steps:")
            print("1. Run: python scripts/train_model.py")
            print("2. Test: python scripts/run_inference.py --mri-image data/mri/test/images/[sample].nii.gz --patient-id test_001")
        else:
            print("\n❌ Dataset setup failed. Check logs for details.")
            sys.exit(1)
            
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
