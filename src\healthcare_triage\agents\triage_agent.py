"""
Triage Agent for healthcare decision making and priority assessment.
"""

import logging
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime

from langchain.agents import AgentExecutor, create_openai_tools_agent
from langchain.tools import Tool
from langchain.prompts import Chat<PERSON>romptTemplate, MessagesPlaceholder
from langchain.schema import BaseMessage, HumanMessage, AIMessage
from langchain_openai import ChatOpenAI
from langchain.memory import ConversationBufferWindowMemory

from ..utils.config import Config

logger = logging.getLogger(__name__)


class TriageAgent:
    """
    LangChain-based agent for medical triage decisions.
    
    This agent:
    - Analyzes patient data and imaging results
    - Assigns triage priority levels
    - Provides reasoning for decisions
    - Suggests next steps and interventions
    """
    
    def __init__(self, config: Config):
        """
        Initialize triage agent.
        
        Args:
            config: Configuration object
        """
        self.config = config
        self.llm = self._setup_llm()
        self.tools = self._setup_tools()
        self.agent = self._setup_agent()
        self.memory = ConversationBufferWindowMemory(
            memory_key="chat_history",
            return_messages=True,
            k=10  # Keep last 10 exchanges
        )
        
    def _setup_llm(self) -> ChatOpenAI:
        """Set up the language model."""
        agent_config = self.config.agents.get("triage_agent", {})
        
        return ChatOpenAI(
            model=agent_config.get("model", "gpt-4"),
            temperature=agent_config.get("temperature", 0.1),
            max_tokens=2048
        )
    
    def _setup_tools(self) -> List[Tool]:
        """Set up tools for the triage agent."""
        tools = [
            Tool(
                name="calculate_risk_score",
                description="Calculate risk score based on patient factors",
                func=self._calculate_risk_score
            ),
            Tool(
                name="assess_urgency",
                description="Assess urgency level based on symptoms and findings",
                func=self._assess_urgency
            ),
            Tool(
                name="recommend_next_steps",
                description="Recommend next steps based on triage assessment",
                func=self._recommend_next_steps
            ),
            Tool(
                name="check_contraindications",
                description="Check for contraindications to proposed treatments",
                func=self._check_contraindications
            ),
            Tool(
                name="estimate_wait_time",
                description="Estimate appropriate wait time based on priority",
                func=self._estimate_wait_time
            )
        ]
        
        return tools
    
    def _setup_agent(self) -> AgentExecutor:
        """Set up the agent executor."""
        
        # Create prompt template
        prompt = ChatPromptTemplate.from_messages([
            ("system", self._get_system_prompt()),
            MessagesPlaceholder(variable_name="chat_history"),
            ("human", "{input}"),
            MessagesPlaceholder(variable_name="agent_scratchpad")
        ])
        
        # Create agent
        agent = create_openai_tools_agent(
            llm=self.llm,
            tools=self.tools,
            prompt=prompt
        )
        
        # Create agent executor
        agent_executor = AgentExecutor(
            agent=agent,
            tools=self.tools,
            memory=self.memory,
            verbose=True,
            max_iterations=self.config.agents.get("triage_agent", {}).get("max_iterations", 5),
            handle_parsing_errors=True
        )
        
        return agent_executor
    
    def _get_system_prompt(self) -> str:
        """Get the system prompt for the triage agent."""
        return """You are an expert medical triage agent responsible for assessing patient priority and making triage decisions.

Your responsibilities:
1. Analyze patient data including symptoms, vital signs, medical history, and imaging results
2. Assign appropriate triage priority levels (Emergency, Urgent, Less Urgent, Non-Urgent)
3. Provide clear reasoning for your decisions
4. Suggest appropriate next steps and interventions
5. Consider patient safety as the top priority

Triage Priority Levels:
- Emergency (Level 1): Life-threatening conditions requiring immediate attention
- Urgent (Level 2): Serious conditions that could deteriorate rapidly
- Less Urgent (Level 3): Stable conditions that need timely care
- Non-Urgent (Level 4): Minor conditions that can wait

Key Considerations:
- Patient vital signs and stability
- Severity and progression of symptoms
- Risk factors and comorbidities
- Imaging findings and their implications
- Available resources and capacity

Always provide:
1. Clear triage priority assignment
2. Detailed reasoning for the decision
3. Specific next steps and recommendations
4. Timeline for reassessment if needed
5. Red flags to watch for

Be thorough, evidence-based, and prioritize patient safety above all else."""
    
    def perform_triage(
        self,
        patient_data: Dict[str, Any],
        imaging_results: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Perform comprehensive triage assessment.
        
        Args:
            patient_data: Patient information and history
            imaging_results: Results from imaging analysis
            
        Returns:
            Triage assessment with priority and recommendations
        """
        # Prepare input for the agent
        triage_input = self._prepare_triage_input(patient_data, imaging_results)
        
        # Run the agent
        try:
            result = self.agent.invoke({"input": triage_input})
            
            # Parse and structure the result
            triage_assessment = self._parse_triage_result(result)
            
            # Add metadata
            triage_assessment.update({
                "timestamp": datetime.now().isoformat(),
                "agent_version": "1.0",
                "patient_id": patient_data.get("patient_id"),
                "input_data": {
                    "has_imaging": imaging_results is not None,
                    "data_sources": list(patient_data.get("raw_data", {}).keys())
                }
            })
            
            logger.info(f"Triage completed for patient {patient_data.get('patient_id')}")
            return triage_assessment
            
        except Exception as e:
            logger.error(f"Triage assessment failed: {e}")
            return self._create_error_response(str(e))
    
    def _prepare_triage_input(
        self,
        patient_data: Dict[str, Any],
        imaging_results: Optional[Dict[str, Any]]
    ) -> str:
        """Prepare input text for the triage agent."""
        
        input_parts = ["PATIENT TRIAGE ASSESSMENT REQUEST\n"]
        
        # Patient summary
        summary = patient_data.get("summary", {})
        if summary:
            input_parts.append("PATIENT SUMMARY:")
            input_parts.append(f"Patient ID: {summary.get('patient_id', 'Unknown')}")
            
            if summary.get("primary_diagnoses"):
                input_parts.append(f"Primary Diagnoses: {', '.join(map(str, summary['primary_diagnoses']))}")
            
            if summary.get("current_medications"):
                input_parts.append(f"Current Medications: {', '.join(map(str, summary['current_medications']))}")
            
            if summary.get("known_allergies"):
                input_parts.append(f"Known Allergies: {', '.join(map(str, summary['known_allergies']))}")
            
            if summary.get("risk_factors"):
                input_parts.append(f"Risk Factors: {', '.join(summary['risk_factors'])}")
            
            input_parts.append("")
        
        # Recent imaging
        if imaging_results:
            input_parts.append("IMAGING RESULTS:")
            input_parts.append(f"Generated Analysis: {imaging_results.get('generated_text', 'No analysis available')}")
            
            seg_info = imaging_results.get('segmentation_info', {})
            if seg_info:
                input_parts.append(f"Tumor Detected: {seg_info.get('has_tumor', 'Unknown')}")
                if seg_info.get('tumor_type'):
                    input_parts.append(f"Tumor Type: {seg_info['tumor_type']}")
                if seg_info.get('location'):
                    input_parts.append(f"Location: {seg_info['location']}")
            
            input_parts.append("")
        
        # Recent medical history
        processed_data = patient_data.get("processed_data", {})
        if processed_data.get("summaries"):
            input_parts.append("RECENT MEDICAL HISTORY:")
            for summary_type, summary_text in processed_data["summaries"].items():
                input_parts.append(f"{summary_type.upper()}: {summary_text}")
            input_parts.append("")
        
        # Timeline of recent events
        timeline = processed_data.get("timeline", [])
        if timeline:
            input_parts.append("RECENT TIMELINE:")
            for event in timeline[-5:]:  # Last 5 events
                input_parts.append(f"- {event.get('date', 'Unknown date')}: {event.get('description', 'No description')}")
            input_parts.append("")
        
        input_parts.append("Please perform a comprehensive triage assessment and provide:")
        input_parts.append("1. Triage priority level (Emergency/Urgent/Less Urgent/Non-Urgent)")
        input_parts.append("2. Detailed reasoning for the priority assignment")
        input_parts.append("3. Specific next steps and recommendations")
        input_parts.append("4. Timeline for reassessment")
        input_parts.append("5. Red flags to monitor")
        
        return "\n".join(input_parts)
    
    def _parse_triage_result(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """Parse the agent's triage result."""
        
        output_text = result.get("output", "")
        
        # Extract priority level
        priority = self._extract_priority(output_text)
        
        # Extract reasoning
        reasoning = self._extract_reasoning(output_text)
        
        # Extract recommendations
        recommendations = self._extract_recommendations(output_text)
        
        # Extract timeline
        timeline = self._extract_timeline(output_text)
        
        # Extract red flags
        red_flags = self._extract_red_flags(output_text)
        
        return {
            "priority_level": priority,
            "reasoning": reasoning,
            "recommendations": recommendations,
            "reassessment_timeline": timeline,
            "red_flags": red_flags,
            "full_response": output_text,
            "confidence_score": self._calculate_confidence(output_text)
        }
    
    def _extract_priority(self, text: str) -> str:
        """Extract priority level from response text."""
        text_lower = text.lower()
        
        if any(term in text_lower for term in ["emergency", "level 1", "immediate"]):
            return "Emergency"
        elif any(term in text_lower for term in ["urgent", "level 2"]):
            return "Urgent"
        elif any(term in text_lower for term in ["less urgent", "level 3"]):
            return "Less Urgent"
        elif any(term in text_lower for term in ["non-urgent", "level 4", "non urgent"]):
            return "Non-Urgent"
        else:
            return "Unknown"
    
    def _extract_reasoning(self, text: str) -> str:
        """Extract reasoning from response text."""
        # Simple extraction - look for reasoning section
        import re
        
        reasoning_patterns = [
            r"reasoning[:\s]+(.*?)(?=\n\n|\nrecommendations|\nnext steps|$)",
            r"rationale[:\s]+(.*?)(?=\n\n|\nrecommendations|\nnext steps|$)",
            r"because[:\s]+(.*?)(?=\n\n|\nrecommendations|\nnext steps|$)"
        ]
        
        for pattern in reasoning_patterns:
            match = re.search(pattern, text, re.IGNORECASE | re.DOTALL)
            if match:
                return match.group(1).strip()
        
        # If no specific reasoning section found, return first paragraph
        paragraphs = text.split('\n\n')
        if len(paragraphs) > 1:
            return paragraphs[1].strip()
        
        return "No specific reasoning provided"
    
    def _extract_recommendations(self, text: str) -> List[str]:
        """Extract recommendations from response text."""
        import re
        
        # Look for recommendations section
        rec_match = re.search(r"(?:recommendations|next steps)[:\s]+(.*?)(?=\n\n|timeline|red flags|$)", 
                             text, re.IGNORECASE | re.DOTALL)
        
        if rec_match:
            rec_text = rec_match.group(1)
            # Split by bullet points or numbers
            recommendations = re.split(r'\n\s*[-•\d+\.]\s*', rec_text)
            return [rec.strip() for rec in recommendations if rec.strip()]
        
        return []
    
    def _extract_timeline(self, text: str) -> str:
        """Extract reassessment timeline from response text."""
        import re
        
        timeline_patterns = [
            r"timeline[:\s]+(.*?)(?=\n\n|red flags|$)",
            r"reassess[:\s]+(.*?)(?=\n\n|red flags|$)",
            r"follow.?up[:\s]+(.*?)(?=\n\n|red flags|$)"
        ]
        
        for pattern in timeline_patterns:
            match = re.search(pattern, text, re.IGNORECASE | re.DOTALL)
            if match:
                return match.group(1).strip()
        
        return "No specific timeline provided"
    
    def _extract_red_flags(self, text: str) -> List[str]:
        """Extract red flags from response text."""
        import re
        
        # Look for red flags section
        flags_match = re.search(r"red flags[:\s]+(.*?)$", text, re.IGNORECASE | re.DOTALL)
        
        if flags_match:
            flags_text = flags_match.group(1)
            # Split by bullet points or numbers
            red_flags = re.split(r'\n\s*[-•\d+\.]\s*', flags_text)
            return [flag.strip() for flag in red_flags if flag.strip()]
        
        return []
    
    def _calculate_confidence(self, text: str) -> float:
        """Calculate confidence score based on response completeness."""
        # Simple heuristic based on response completeness
        score = 0.0
        
        if "priority" in text.lower() or "level" in text.lower():
            score += 0.2
        if "reasoning" in text.lower() or "because" in text.lower():
            score += 0.2
        if "recommend" in text.lower() or "next step" in text.lower():
            score += 0.2
        if "timeline" in text.lower() or "reassess" in text.lower():
            score += 0.2
        if "red flag" in text.lower() or "warning" in text.lower():
            score += 0.2
        
        return min(score, 1.0)
    
    def _create_error_response(self, error_message: str) -> Dict[str, Any]:
        """Create error response for failed triage."""
        return {
            "priority_level": "Unknown",
            "reasoning": f"Triage assessment failed: {error_message}",
            "recommendations": ["Manual review required", "Consult with physician"],
            "reassessment_timeline": "Immediate",
            "red_flags": ["System error - manual assessment needed"],
            "full_response": f"ERROR: {error_message}",
            "confidence_score": 0.0,
            "error": True,
            "timestamp": datetime.now().isoformat()
        }
    
    # Tool functions
    def _calculate_risk_score(self, patient_factors: str) -> str:
        """Calculate risk score based on patient factors."""
        # Simplified risk scoring
        risk_score = 0
        factors = patient_factors.lower()
        
        # Age factors
        if "elderly" in factors or "age > 65" in factors:
            risk_score += 2
        
        # Comorbidity factors
        high_risk_conditions = ["diabetes", "hypertension", "cancer", "heart disease", "stroke"]
        for condition in high_risk_conditions:
            if condition in factors:
                risk_score += 1
        
        # Severity indicators
        if "severe" in factors or "critical" in factors:
            risk_score += 3
        elif "moderate" in factors:
            risk_score += 2
        elif "mild" in factors:
            risk_score += 1
        
        return f"Risk score: {risk_score}/10 (Low: 0-3, Moderate: 4-6, High: 7-10)"
    
    def _assess_urgency(self, symptoms_findings: str) -> str:
        """Assess urgency level based on symptoms and findings."""
        symptoms = symptoms_findings.lower()
        
        # Emergency indicators
        emergency_signs = ["unconscious", "severe bleeding", "chest pain", "difficulty breathing", 
                          "stroke symptoms", "severe trauma", "cardiac arrest"]
        
        for sign in emergency_signs:
            if sign in symptoms:
                return "Emergency - Immediate attention required"
        
        # Urgent indicators
        urgent_signs = ["moderate pain", "fever", "vomiting", "confusion", "rapid heart rate"]
        
        for sign in urgent_signs:
            if sign in symptoms:
                return "Urgent - Attention needed within 1-2 hours"
        
        return "Less urgent - Can wait for standard care"
    
    def _recommend_next_steps(self, assessment_data: str) -> str:
        """Recommend next steps based on triage assessment."""
        data = assessment_data.lower()
        
        recommendations = []
        
        if "emergency" in data:
            recommendations.extend([
                "Immediate physician evaluation",
                "Continuous monitoring",
                "Prepare for potential intervention"
            ])
        elif "urgent" in data:
            recommendations.extend([
                "Physician evaluation within 1-2 hours",
                "Regular vital sign monitoring",
                "Pain management if needed"
            ])
        else:
            recommendations.extend([
                "Standard care pathway",
                "Routine monitoring",
                "Patient education and discharge planning"
            ])
        
        return "; ".join(recommendations)
    
    def _check_contraindications(self, treatment_plan: str) -> str:
        """Check for contraindications to proposed treatments."""
        # Simplified contraindication checking
        plan = treatment_plan.lower()
        
        contraindications = []
        
        if "mri" in plan and "pacemaker" in plan:
            contraindications.append("MRI contraindicated with pacemaker")
        
        if "contrast" in plan and "kidney" in plan:
            contraindications.append("Contrast may be contraindicated with kidney disease")
        
        if contraindications:
            return "Contraindications found: " + "; ".join(contraindications)
        else:
            return "No obvious contraindications identified"
    
    def _estimate_wait_time(self, priority_level: str) -> str:
        """Estimate appropriate wait time based on priority."""
        priority = priority_level.lower()
        
        if "emergency" in priority:
            return "0 minutes - Immediate"
        elif "urgent" in priority:
            return "15-60 minutes"
        elif "less urgent" in priority:
            return "1-4 hours"
        else:
            return "4+ hours or next available appointment"
