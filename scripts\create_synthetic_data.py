#!/usr/bin/env python3
"""
Create synthetic brain MRI data for demonstration purposes.
This creates a small dataset that mimics the structure of real brain MRI data.
"""

import os
import sys
import numpy as np
import nibabel as nib
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from healthcare_triage.utils.config import load_config
from healthcare_triage.utils.logging_utils import setup_comprehensive_logging


def create_synthetic_brain_image(shape=(64, 64, 64), seed=None):
    """Create a synthetic brain-like image."""
    if seed is not None:
        np.random.seed(seed)
    
    # Create base brain structure
    x, y, z = np.meshgrid(
        np.linspace(-1, 1, shape[0]),
        np.linspace(-1, 1, shape[1]),
        np.linspace(-1, 1, shape[2]),
        indexing='ij'
    )
    
    # Create brain-like ellipsoid
    brain_mask = (x**2 + y**2 + z**2) < 0.8
    
    # Add some structure
    image = np.zeros(shape, dtype=np.float32)
    
    # Gray matter (higher intensity)
    gray_matter = ((x**2 + y**2 + z**2) < 0.6) & ((x**2 + y**2 + z**2) > 0.3)
    image[gray_matter] = 0.7 + 0.1 * np.random.random(np.sum(gray_matter))
    
    # White matter (medium intensity)
    white_matter = (x**2 + y**2 + z**2) < 0.3
    image[white_matter] = 0.5 + 0.1 * np.random.random(np.sum(white_matter))
    
    # CSF (low intensity)
    csf = brain_mask & ~gray_matter & ~white_matter
    image[csf] = 0.2 + 0.05 * np.random.random(np.sum(csf))
    
    # Add some noise
    image += 0.02 * np.random.random(shape)
    
    # Apply brain mask
    image[~brain_mask] = 0
    
    return image


def create_synthetic_segmentation(brain_image, seed=None):
    """Create a synthetic segmentation mask."""
    if seed is not None:
        np.random.seed(seed)
    
    shape = brain_image.shape
    x, y, z = np.meshgrid(
        np.linspace(-1, 1, shape[0]),
        np.linspace(-1, 1, shape[1]),
        np.linspace(-1, 1, shape[2]),
        indexing='ij'
    )
    
    # Create some "lesions" or regions of interest
    segmentation = np.zeros(shape, dtype=np.float32)
    
    # Add a few random lesions
    for i in range(3):
        # Random center
        cx = np.random.uniform(-0.5, 0.5)
        cy = np.random.uniform(-0.5, 0.5)
        cz = np.random.uniform(-0.5, 0.5)
        
        # Random size
        radius = np.random.uniform(0.1, 0.2)
        
        # Create lesion
        lesion = ((x - cx)**2 + (y - cy)**2 + (z - cz)**2) < radius**2
        lesion = lesion & (brain_image > 0)  # Only where brain exists
        
        segmentation[lesion] = 1.0
    
    return segmentation


def create_synthetic_dataset(output_dir, num_train=10, num_val=5, num_test=3):
    """Create a complete synthetic dataset."""
    output_dir = Path(output_dir)
    
    # Create directory structure
    for split in ['train', 'val', 'test']:
        (output_dir / split / 'images').mkdir(parents=True, exist_ok=True)
        (output_dir / split / 'labels').mkdir(parents=True, exist_ok=True)
    
    # Create training data
    print(f"Creating {num_train} training samples...")
    for i in range(num_train):
        # Create brain image
        brain_img = create_synthetic_brain_image(seed=i)
        segmentation = create_synthetic_segmentation(brain_img, seed=i)
        
        # Create NIfTI images
        brain_nii = nib.Nifti1Image(brain_img, np.eye(4))
        seg_nii = nib.Nifti1Image(segmentation, np.eye(4))
        
        # Save files
        patient_id = f"patient_{i:03d}"
        brain_path = output_dir / 'train' / 'images' / f"{patient_id}_image.nii.gz"
        seg_path = output_dir / 'train' / 'labels' / f"{patient_id}_label.nii.gz"
        
        nib.save(brain_nii, str(brain_path))
        nib.save(seg_nii, str(seg_path))
        
        print(f"  Created {patient_id}")
    
    # Create validation data
    print(f"Creating {num_val} validation samples...")
    for i in range(num_val):
        brain_img = create_synthetic_brain_image(seed=i + 1000)
        segmentation = create_synthetic_segmentation(brain_img, seed=i + 1000)
        
        brain_nii = nib.Nifti1Image(brain_img, np.eye(4))
        seg_nii = nib.Nifti1Image(segmentation, np.eye(4))
        
        patient_id = f"patient_val_{i:03d}"
        brain_path = output_dir / 'val' / 'images' / f"{patient_id}_image.nii.gz"
        seg_path = output_dir / 'val' / 'labels' / f"{patient_id}_label.nii.gz"
        
        nib.save(brain_nii, str(brain_path))
        nib.save(seg_nii, str(seg_path))
        
        print(f"  Created {patient_id}")
    
    # Create test data
    print(f"Creating {num_test} test samples...")
    for i in range(num_test):
        brain_img = create_synthetic_brain_image(seed=i + 2000)
        segmentation = create_synthetic_segmentation(brain_img, seed=i + 2000)
        
        brain_nii = nib.Nifti1Image(brain_img, np.eye(4))
        seg_nii = nib.Nifti1Image(segmentation, np.eye(4))
        
        patient_id = f"patient_test_{i:03d}"
        brain_path = output_dir / 'test' / 'images' / f"{patient_id}_image.nii.gz"
        seg_path = output_dir / 'test' / 'labels' / f"{patient_id}_label.nii.gz"
        
        nib.save(brain_nii, str(brain_path))
        nib.save(seg_nii, str(seg_path))
        
        print(f"  Created {patient_id}")
    
    print(f"\n✅ Synthetic dataset created in {output_dir}")
    print(f"   - Training: {num_train} samples")
    print(f"   - Validation: {num_val} samples")
    print(f"   - Test: {num_test} samples")
    
    return output_dir


def create_patient_records(output_dir, num_patients=18):
    """Create synthetic patient records."""
    records_dir = Path(output_dir) / "patient_records"
    records_dir.mkdir(exist_ok=True)
    
    print(f"Creating {num_patients} patient records...")
    
    # Sample medical histories
    symptoms = [
        "headache", "dizziness", "nausea", "vision problems", "memory loss",
        "seizures", "weakness", "numbness", "speech difficulties", "confusion"
    ]
    
    conditions = [
        "migraine", "tension headache", "brain tumor", "stroke", "epilepsy",
        "multiple sclerosis", "Alzheimer's disease", "Parkinson's disease"
    ]
    
    for i in range(num_patients):
        patient_id = f"patient_{i:03d}" if i < 10 else f"patient_val_{i-10:03d}" if i < 15 else f"patient_test_{i-15:03d}"
        
        # Generate synthetic patient data
        age = np.random.randint(25, 85)
        gender = np.random.choice(["Male", "Female"])
        
        # Random symptoms
        patient_symptoms = np.random.choice(symptoms, size=np.random.randint(1, 4), replace=False)
        
        # Random medical history
        history_conditions = np.random.choice(conditions, size=np.random.randint(0, 2), replace=False)
        
        # Create patient record
        record = {
            "patient_id": patient_id,
            "age": age,
            "gender": gender,
            "chief_complaint": f"Patient presents with {', '.join(patient_symptoms)}",
            "medical_history": f"History of {', '.join(history_conditions)}" if len(history_conditions) > 0 else "No significant medical history",
            "current_symptoms": list(patient_symptoms),
            "duration": f"{np.random.randint(1, 30)} days",
            "severity": np.random.choice(["mild", "moderate", "severe"]),
            "imaging_requested": "Brain MRI with contrast",
            "clinical_notes": f"Patient is a {age}-year-old {gender.lower()} presenting with {', '.join(patient_symptoms)}. Neurological examination pending."
        }
        
        # Save as JSON
        import json
        record_path = records_dir / f"{patient_id}_record.json"
        with open(record_path, 'w') as f:
            json.dump(record, f, indent=2)
        
        print(f"  Created record for {patient_id}")
    
    print(f"✅ Patient records created in {records_dir}")


def main():
    """Main function."""
    try:
        print("🧬 CREATING SYNTHETIC HEALTHCARE DATA")
        print("=" * 50)
        
        # Load config
        config = load_config()
        
        # Create synthetic MRI dataset
        mri_data_dir = Path(config.data.mri_data_path)
        create_synthetic_dataset(mri_data_dir)
        
        # Create patient records
        create_patient_records(mri_data_dir.parent)
        
        print("\n🎉 Synthetic data creation completed!")
        print("\nDataset structure:")
        print(f"  📁 {mri_data_dir}/")
        print("    📁 train/")
        print("      📁 images/ (10 brain MRI scans)")
        print("      📁 labels/ (10 segmentation masks)")
        print("    📁 val/")
        print("      📁 images/ (5 brain MRI scans)")
        print("      📁 labels/ (5 segmentation masks)")
        print("    📁 test/")
        print("      📁 images/ (3 brain MRI scans)")
        print("      📁 labels/ (3 segmentation masks)")
        print(f"  📁 {mri_data_dir.parent}/patient_records/ (18 patient records)")
        
        print("\nNext steps:")
        print("1. Run training: python scripts/train_simple.py")
        print("2. Run inference: python scripts/run_inference.py")
        
    except Exception as e:
        print(f"❌ Synthetic data creation failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
