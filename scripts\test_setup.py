#!/usr/bin/env python3
"""
Test script to verify the Healthcare Triage Agent setup.
"""

import os
import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

def test_imports():
    """Test that all core modules can be imported."""
    print("🧪 Testing imports...")
    
    try:
        # Test basic imports
        import torch
        print(f"✅ PyTorch: {torch.__version__}")
        
        import nibabel as nib
        print(f"✅ NiBabel: {nib.__version__}")
        
        import numpy as np
        print(f"✅ NumPy: {np.__version__}")
        
        # Test MONAI
        import monai
        print(f"✅ MONAI: {monai.__version__}")
        
        # Test our modules
        from healthcare_triage.utils.config import load_config
        print("✅ Config module")
        
        from healthcare_triage.utils.logging_utils import setup_comprehensive_logging
        print("✅ Logging module")
        
        from healthcare_triage.imaging import MRIProcessor
        print("✅ MRI Processor")
        
        from healthcare_triage.nlp import PatientHistoryProcessor
        print("✅ Patient History Processor")
        
        # Test PaliGemma (might fail without proper setup)
        try:
            from healthcare_triage.imaging import PaliGemmaSegmenter
            print("✅ PaliGemma Segmenter")
        except Exception as e:
            print(f"⚠️  PaliGemma Segmenter: {e}")
        
        # Test agents (might fail without API keys)
        try:
            from healthcare_triage.agents import TriageAgent
            print("✅ Triage Agent")
        except Exception as e:
            print(f"⚠️  Triage Agent: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Import failed: {e}")
        return False


def test_configuration():
    """Test configuration loading."""
    print("\n⚙️  Testing configuration...")
    
    try:
        from healthcare_triage.utils.config import load_config
        
        config = load_config()
        print("✅ Default configuration loaded")
        
        # Test config access
        print(f"   - MRI data path: {config.data.mri_data_path}")
        print(f"   - Model name: {config.models.paligemma.model_name}")
        print(f"   - Image size: {config.preprocessing.image_size}")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False


def test_data_structure():
    """Test data directory structure."""
    print("\n📁 Testing data structure...")
    
    required_dirs = [
        "data",
        "data/mri",
        "models",
        "configs",
        "scripts",
        "src/healthcare_triage"
    ]
    
    all_exist = True
    for directory in required_dirs:
        if Path(directory).exists():
            print(f"✅ {directory}")
        else:
            print(f"❌ {directory} (missing)")
            all_exist = False
    
    return all_exist


def test_synthetic_data_creation():
    """Test synthetic data creation."""
    print("\n🧬 Testing synthetic data creation...")
    
    try:
        import numpy as np
        import nibabel as nib
        
        # Create a simple synthetic brain image
        brain_shape = (64, 64, 64)  # Smaller for testing
        brain_image = np.random.random(brain_shape).astype(np.float32)
        
        # Create NIfTI image
        nii_img = nib.Nifti1Image(brain_image, np.eye(4))
        
        # Save test image
        test_dir = Path("data/test")
        test_dir.mkdir(exist_ok=True)
        
        test_path = test_dir / "test_brain.nii.gz"
        nib.save(nii_img, str(test_path))
        
        # Verify we can load it back
        loaded_img = nib.load(str(test_path))
        loaded_data = loaded_img.get_fdata()
        
        print(f"✅ Created and loaded test NIfTI image: {loaded_data.shape}")
        
        # Clean up
        test_path.unlink()
        
        return True
        
    except Exception as e:
        print(f"❌ Synthetic data test failed: {e}")
        return False


def test_mri_processing():
    """Test MRI processing pipeline."""
    print("\n🧠 Testing MRI processing...")
    
    try:
        from healthcare_triage.imaging import MRIProcessor
        from healthcare_triage.utils.config import load_config
        
        config = load_config()
        processor = MRIProcessor(config)
        
        print("✅ MRI Processor initialized")
        
        # Test preprocessing components
        from healthcare_triage.imaging.preprocessing import MRIPreprocessor
        preprocessor = MRIPreprocessor(config)
        print("✅ MRI Preprocessor initialized")
        
        # Test augmentation
        from healthcare_triage.imaging.augmentation import MRIAugmentation
        augmenter = MRIAugmentation(config)
        print("✅ MRI Augmentation initialized")
        
        return True
        
    except Exception as e:
        print(f"❌ MRI processing test failed: {e}")
        return False


def test_patient_processing():
    """Test patient history processing."""
    print("\n📄 Testing patient history processing...")
    
    try:
        from healthcare_triage.nlp import PatientHistoryProcessor
        from healthcare_triage.utils.config import load_config
        
        config = load_config()
        processor = PatientHistoryProcessor(config)
        
        print("✅ Patient History Processor initialized")
        
        # Test document parser
        from healthcare_triage.nlp.document_parser import DocumentParser
        parser = DocumentParser(config)
        print("✅ Document Parser initialized")
        
        return True
        
    except Exception as e:
        print(f"❌ Patient processing test failed: {e}")
        return False


def main():
    """Run all tests."""
    print("🚀 HEALTHCARE TRIAGE AGENT - SETUP TEST")
    print("=" * 50)
    
    tests = [
        ("Imports", test_imports),
        ("Configuration", test_configuration),
        ("Data Structure", test_data_structure),
        ("Synthetic Data", test_synthetic_data_creation),
        ("MRI Processing", test_mri_processing),
        ("Patient Processing", test_patient_processing),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n📊 TEST SUMMARY")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! Setup is working correctly.")
        return 0
    else:
        print(f"\n⚠️  {total - passed} tests failed. Check the output above for details.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
