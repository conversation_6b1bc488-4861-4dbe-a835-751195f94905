#!/usr/bin/env python3
"""
Comprehensive inference script for Healthcare Multimodal Triage Agent.

This script demonstrates the complete pipeline:
1. Load patient data and medical history
2. Process brain MRI images
3. Generate segmentation predictions
4. Provide triage recommendations
"""

import os
import sys
import json
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, Optional, List

import torch
import numpy as np
import nibabel as nib

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from healthcare_triage.imaging import MRIProcessor
from healthcare_triage.nlp import PatientHistoryProcessor
from healthcare_triage.utils.config import load_config
from healthcare_triage.utils.logging_utils import setup_comprehensive_logging


class SimpleSegmentationModel(torch.nn.Module):
    """Simple 3D U-Net-like model for demonstration."""

    def __init__(self, in_channels=1, out_channels=1):
        """Initialize model."""
        super().__init__()

        # Simple encoder-decoder architecture
        self.encoder = torch.nn.Sequential(
            torch.nn.Conv3d(in_channels, 32, 3, padding=1),
            torch.nn.ReLU(),
            torch.nn.Conv3d(32, 64, 3, padding=1),
            torch.nn.ReLU(),
            torch.nn.AdaptiveAvgPool3d((32, 32, 32))
        )

        self.decoder = torch.nn.Sequential(
            torch.nn.Conv3d(64, 32, 3, padding=1),
            torch.nn.ReLU(),
            torch.nn.Conv3d(32, out_channels, 3, padding=1),
            torch.nn.Sigmoid()
        )

    def forward(self, x):
        """Forward pass."""
        # Encode
        encoded = self.encoder(x)

        # Decode
        decoded = self.decoder(encoded)

        # Resize to match input
        decoded = torch.nn.functional.interpolate(
            decoded, size=x.shape[2:], mode='trilinear', align_corners=False
        )

        return decoded


class HealthcareTriageInference:
    """Complete healthcare triage inference system."""
    
    def __init__(self, config_path: Optional[str] = None, model_path: Optional[str] = None):
        """Initialize inference system."""
        self.config = load_config(config_path)
        self.logger = setup_comprehensive_logging(self.config)
        
        # Set device
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.logger.info(f"Using device: {self.device}")
        
        # Initialize processors
        self.mri_processor = MRIProcessor(self.config)
        self.patient_processor = PatientHistoryProcessor(self.config)
        
        # Load model
        self.model = self._load_model(model_path)
        
        self.logger.info("Healthcare Triage Inference system initialized")
    
    def _load_model(self, model_path: Optional[str] = None):
        """Load the trained model."""
        if model_path is None:
            # Find the latest checkpoint
            checkpoint_dir = Path("models/checkpoints")
            if checkpoint_dir.exists():
                checkpoints = list(checkpoint_dir.glob("*.pth"))
                if checkpoints:
                    model_path = max(checkpoints, key=lambda x: x.stat().st_mtime)
                    self.logger.info(f"Loading latest checkpoint: {model_path}")
        
        if model_path and Path(model_path).exists():
            # Load the simple model architecture
            model = SimpleSegmentationModel().to(self.device)

            # Load checkpoint
            checkpoint = torch.load(model_path, map_location=self.device)
            model.load_state_dict(checkpoint['model_state_dict'])
            model.eval()

            self.logger.info(f"Model loaded from {model_path}")
            return model
        else:
            self.logger.warning("No trained model found. Using untrained model.")
            model = SimpleSegmentationModel().to(self.device)
            model.eval()
            return model
    
    def load_patient_data(self, patient_id: str) -> Dict[str, Any]:
        """Load comprehensive patient data."""
        patient_data = {}
        
        # Load patient record
        record_path = Path("data/patient_records") / f"{patient_id}_record.json"
        if record_path.exists():
            with open(record_path, 'r') as f:
                patient_data['record'] = json.load(f)
            self.logger.info(f"Loaded patient record for {patient_id}")
        else:
            self.logger.warning(f"No patient record found for {patient_id}")
            patient_data['record'] = {"patient_id": patient_id}
        
        # Load processed patient history
        try:
            history_data = self.patient_processor.load_patient_data(patient_id)
            processed_history = self.patient_processor.process_patient_history(history_data)
            patient_data['history'] = processed_history
            self.logger.info(f"Loaded patient history for {patient_id}")
        except Exception as e:
            self.logger.warning(f"Could not load patient history for {patient_id}: {e}")
            patient_data['history'] = {}
        
        return patient_data
    
    def process_mri_image(self, image_tensor: torch.Tensor) -> torch.Tensor:
        """Process MRI image and generate segmentation."""
        with torch.no_grad():
            image = image_tensor.to(self.device)
            
            # Add batch dimension if needed
            if image.ndim == 4:
                image = image.unsqueeze(0)
            
            # Generate prediction
            prediction = self.model(image)
            
            # Remove batch dimension
            if prediction.ndim == 5:
                prediction = prediction.squeeze(0)
            
            return prediction.cpu()
    
    def analyze_segmentation(self, segmentation: torch.Tensor, threshold: float = 0.5) -> Dict[str, Any]:
        """Analyze segmentation results."""
        # Convert to binary mask
        binary_mask = (segmentation > threshold).float()
        
        # Calculate metrics
        total_voxels = binary_mask.numel()
        positive_voxels = binary_mask.sum().item()
        volume_fraction = positive_voxels / total_voxels
        
        # Estimate volume (assuming 1mm³ voxels)
        estimated_volume_ml = positive_voxels * 0.001  # Convert mm³ to ml
        
        analysis = {
            "total_voxels": total_voxels,
            "positive_voxels": int(positive_voxels),
            "volume_fraction": volume_fraction,
            "estimated_volume_ml": estimated_volume_ml,
            "max_intensity": segmentation.max().item(),
            "mean_intensity": segmentation.mean().item(),
            "threshold_used": threshold
        }
        
        return analysis
    
    def generate_triage_recommendation(self, patient_data: Dict[str, Any], 
                                     segmentation_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Generate triage recommendation based on all available data."""
        
        # Extract key information
        record = patient_data.get('record', {})
        history = patient_data.get('history', {})
        
        # Risk factors
        risk_factors = []
        urgency_score = 0
        
        # Age-based risk
        age = record.get('age', 0)
        if age > 65:
            risk_factors.append("Advanced age (>65)")
            urgency_score += 1
        
        # Symptom-based risk
        symptoms = record.get('current_symptoms', [])
        high_risk_symptoms = ['seizures', 'vision problems', 'speech difficulties', 'weakness']
        for symptom in symptoms:
            if symptom in high_risk_symptoms:
                risk_factors.append(f"High-risk symptom: {symptom}")
                urgency_score += 2
        
        # Imaging findings
        volume_fraction = segmentation_analysis.get('volume_fraction', 0)
        estimated_volume = segmentation_analysis.get('estimated_volume_ml', 0)
        
        if volume_fraction > 0.01:  # >1% of brain volume
            risk_factors.append(f"Significant imaging findings (volume: {estimated_volume:.2f}ml)")
            urgency_score += 3
        elif volume_fraction > 0.005:  # >0.5% of brain volume
            risk_factors.append(f"Moderate imaging findings (volume: {estimated_volume:.2f}ml)")
            urgency_score += 2
        elif volume_fraction > 0.001:  # >0.1% of brain volume
            risk_factors.append(f"Minor imaging findings (volume: {estimated_volume:.2f}ml)")
            urgency_score += 1
        
        # Determine triage level
        if urgency_score >= 5:
            triage_level = "URGENT"
            recommendation = "Immediate neurology consultation and further imaging recommended"
            timeframe = "Within 24 hours"
        elif urgency_score >= 3:
            triage_level = "SEMI-URGENT"
            recommendation = "Neurology consultation recommended within 1-2 weeks"
            timeframe = "Within 1-2 weeks"
        elif urgency_score >= 1:
            triage_level = "ROUTINE"
            recommendation = "Routine follow-up with primary care physician"
            timeframe = "Within 1 month"
        else:
            triage_level = "LOW"
            recommendation = "No immediate action required, routine monitoring"
            timeframe = "As needed"
        
        return {
            "patient_id": record.get('patient_id', 'unknown'),
            "triage_level": triage_level,
            "urgency_score": urgency_score,
            "risk_factors": risk_factors,
            "recommendation": recommendation,
            "timeframe": timeframe,
            "imaging_summary": {
                "volume_fraction": volume_fraction,
                "estimated_volume_ml": estimated_volume,
                "findings": "Abnormal findings detected" if volume_fraction > 0.001 else "No significant findings"
            },
            "generated_at": datetime.now().isoformat()
        }
    
    def run_inference_on_patient(self, patient_id: str) -> Dict[str, Any]:
        """Run complete inference pipeline on a single patient."""
        self.logger.info(f"Running inference for patient {patient_id}")
        
        # Load patient data
        patient_data = self.load_patient_data(patient_id)
        
        # Find and load MRI image
        mri_path = Path(f"data/mri/test/images/{patient_id}_image.nii.gz")
        if not mri_path.exists():
            # Try other directories
            for split in ['train', 'val']:
                alt_path = Path(f"data/mri/{split}/images/{patient_id}_image.nii.gz")
                if alt_path.exists():
                    mri_path = alt_path
                    break
        
        if not mri_path.exists():
            raise FileNotFoundError(f"MRI image not found for patient {patient_id}")
        
        # Load and process MRI
        img_nii = nib.load(str(mri_path))
        image = torch.from_numpy(img_nii.get_fdata()).float().unsqueeze(0)
        
        # Generate segmentation
        segmentation = self.process_mri_image(image)
        
        # Analyze segmentation
        seg_analysis = self.analyze_segmentation(segmentation)
        
        # Generate triage recommendation
        triage_result = self.generate_triage_recommendation(patient_data, seg_analysis)
        
        # Save results
        output_dir = Path("outputs/inference")
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # Save segmentation
        seg_nii = nib.Nifti1Image(segmentation.squeeze().numpy(), img_nii.affine)
        seg_path = output_dir / f"{patient_id}_segmentation.nii.gz"
        nib.save(seg_nii, str(seg_path))
        
        # Save triage report
        report_path = output_dir / f"{patient_id}_triage_report.json"
        with open(report_path, 'w') as f:
            json.dump(triage_result, f, indent=2)
        
        self.logger.info(f"Inference completed for {patient_id}")
        self.logger.info(f"Triage level: {triage_result['triage_level']}")
        self.logger.info(f"Recommendation: {triage_result['recommendation']}")
        
        return triage_result
    
    def run_batch_inference(self, patient_ids: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        """Run inference on multiple patients."""
        if patient_ids is None:
            # Get all test patients
            test_dir = Path("data/mri/test/images")
            if test_dir.exists():
                patient_ids = []
                for f in test_dir.glob("*.nii.gz"):
                    # Extract patient ID properly
                    patient_id = f.stem.replace("_image", "")
                    if patient_id.endswith(".nii"):
                        patient_id = patient_id[:-4]  # Remove .nii extension
                    patient_ids.append(patient_id)
            else:
                patient_ids = []

        results = []
        for patient_id in patient_ids:
            try:
                result = self.run_inference_on_patient(patient_id)
                results.append(result)
            except Exception as e:
                self.logger.error(f"Failed to process patient {patient_id}: {e}")

        return results


def main():
    """Main function."""
    try:
        print("🏥 HEALTHCARE TRIAGE AGENT - COMPREHENSIVE INFERENCE")
        print("=" * 60)
        
        # Initialize inference system
        inference_system = HealthcareTriageInference()
        
        # Run batch inference on test patients
        print("\n🔍 Running inference on test patients...")
        results = inference_system.run_batch_inference()
        
        # Display results
        print(f"\n📊 TRIAGE RESULTS ({len(results)} patients)")
        print("=" * 60)
        
        for result in results:
            print(f"\n👤 Patient: {result['patient_id']}")
            print(f"   🚨 Triage Level: {result['triage_level']}")
            print(f"   ⏰ Timeframe: {result['timeframe']}")
            print(f"   💡 Recommendation: {result['recommendation']}")
            print(f"   🧠 Imaging: {result['imaging_summary']['findings']}")
            if result['risk_factors']:
                print(f"   ⚠️  Risk Factors: {', '.join(result['risk_factors'])}")
        
        print(f"\n✅ Inference completed!")
        print(f"📁 Results saved in outputs/inference/")
        print(f"   - Segmentation masks: *_segmentation.nii.gz")
        print(f"   - Triage reports: *_triage_report.json")
        
    except Exception as e:
        print(f"❌ Inference failed: {e}")
        logging.error(f"Inference failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
