"""
Imaging module for brain MRI processing and segmentation.

This module provides functionality for:
- MRI image preprocessing using MONAI
- PaliGemma 2 model integration for tumor segmentation
- Image augmentation and normalization
- Segmentation post-processing
"""

from .mri_processor import MRIProcessor
from .preprocessing import MRIPreprocessor
from .augmentation import MRIAugmentation
from .postprocessing import SegmentationPostProcessor

# Lazy import for PaliGemma to handle potential dependency issues
def _get_paligemma_segmenter():
    """Lazy import of PaliGemma segmenter."""
    try:
        from .paligemma_segmenter import PaliGemmaSegmenter
        return PaliGemmaSegmenter
    except (ImportError, RuntimeError) as e:
        print(f"Warning: PaliGemma segmenter not available: {e}")
        return None

# Make PaliGemmaSegmenter available
PaliGemmaSegmenter = _get_paligemma_segmenter()

__all__ = [
    "MRIProcessor",
    "PaliGemmaSegmenter",
    "MRIPreprocessor",
    "MRIAugmentation",
    "SegmentationPostProcessor",
]
