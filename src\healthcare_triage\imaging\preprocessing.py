"""
MRI preprocessing utilities using MONAI.
"""

import logging
from typing import Dict, List, Optional, Tuple, Union

import torch
import numpy as np
import SimpleITK as sitk
from monai.transforms import (
    Compose, LoadImage, EnsureChannelFirst, Orientation,
    Spacing, ScaleIntensityRange, CropForeground,
    SpatialPad, Resize, NormalizeIntensity
)

from ..utils.config import Config

logger = logging.getLogger(__name__)


class MRIPreprocessor:
    """
    MRI preprocessing pipeline using MONAI transforms.
    
    Handles:
    - Bias field correction
    - Intensity normalization
    - Spatial resampling
    - Image registration
    - Skull stripping (optional)
    """
    
    def __init__(self, config: Config):
        """
        Initialize preprocessor with configuration.
        
        Args:
            config: Configuration object
        """
        self.config = config
        self.setup_transforms()
        
    def setup_transforms(self):
        """Set up preprocessing transforms."""
        
        # Basic preprocessing pipeline
        self.basic_transforms = Compose([
            LoadImage(image_only=True),
            EnsureChannelFirst(),
            Orientation(axcodes="RAS"),
            Spacing(
                pixdim=self.config.preprocessing.spacing,
                mode="bilinear"
            ),
            ScaleIntensityRange(
                a_min=0, a_max=1000,
                b_min=0.0, b_max=1.0,
                clip=True
            ),
            CropForeground(),
        ])
        
        # Advanced preprocessing with normalization
        self.advanced_transforms = Compose([
            self.basic_transforms,
            NormalizeIntensity(nonzero=True),
            SpatialPad(
                spatial_size=self.config.preprocessing.image_size,
                mode="constant"
            ),
            Resize(
                spatial_size=self.config.preprocessing.image_size,
                mode="trilinear"
            )
        ])
    
    def bias_field_correction(self, image: Union[torch.Tensor, np.ndarray]) -> np.ndarray:
        """
        Apply N4 bias field correction using SimpleITK.
        
        Args:
            image: Input MRI image
            
        Returns:
            Bias field corrected image
        """
        # Convert to SimpleITK image
        if isinstance(image, torch.Tensor):
            image = image.cpu().numpy()
            
        if image.ndim == 4:  # Remove batch dimension
            image = image[0]
        if image.ndim == 3 and image.shape[0] == 1:  # Remove channel dimension
            image = image[0]
            
        sitk_image = sitk.GetImageFromArray(image.astype(np.float32))
        
        # Apply N4 bias field correction
        corrector = sitk.N4BiasFieldCorrectionImageFilter()
        corrector.SetMaximumNumberOfIterations([4] * 4)
        
        try:
            corrected_image = corrector.Execute(sitk_image)
            corrected_array = sitk.GetArrayFromImage(corrected_image)
            logger.info("Bias field correction applied successfully")
            return corrected_array
        except Exception as e:
            logger.warning(f"Bias field correction failed: {e}. Returning original image.")
            return image
    
    def intensity_normalization(
        self, 
        image: Union[torch.Tensor, np.ndarray],
        method: str = "z_score"
    ) -> np.ndarray:
        """
        Apply intensity normalization.
        
        Args:
            image: Input image
            method: Normalization method ("z_score", "min_max", "percentile")
            
        Returns:
            Normalized image
        """
        if isinstance(image, torch.Tensor):
            image = image.cpu().numpy()
            
        if method == "z_score":
            # Z-score normalization
            mean = np.mean(image[image > 0])  # Exclude background
            std = np.std(image[image > 0])
            normalized = (image - mean) / (std + 1e-8)
            
        elif method == "min_max":
            # Min-max normalization
            min_val = np.min(image)
            max_val = np.max(image)
            normalized = (image - min_val) / (max_val - min_val + 1e-8)
            
        elif method == "percentile":
            # Percentile-based normalization
            p1, p99 = np.percentile(image[image > 0], [1, 99])
            normalized = np.clip((image - p1) / (p99 - p1), 0, 1)
            
        else:
            raise ValueError(f"Unknown normalization method: {method}")
            
        return normalized.astype(np.float32)
    
    def skull_stripping(self, image: Union[torch.Tensor, np.ndarray]) -> np.ndarray:
        """
        Simple skull stripping using thresholding and morphological operations.
        
        Args:
            image: Input MRI image
            
        Returns:
            Skull-stripped image
        """
        if isinstance(image, torch.Tensor):
            image = image.cpu().numpy()
            
        # Simple thresholding approach
        # In practice, you might want to use more sophisticated methods like BET
        threshold = np.percentile(image[image > 0], 10)
        mask = image > threshold
        
        # Apply morphological operations to clean up the mask
        from scipy import ndimage
        
        # Fill holes
        mask = ndimage.binary_fill_holes(mask)
        
        # Remove small objects
        mask = ndimage.binary_opening(mask, structure=np.ones((3, 3, 3)))
        
        # Apply mask
        skull_stripped = image * mask
        
        logger.info("Skull stripping applied")
        return skull_stripped.astype(np.float32)
    
    def register_images(
        self,
        moving_image: Union[torch.Tensor, np.ndarray],
        fixed_image: Union[torch.Tensor, np.ndarray]
    ) -> np.ndarray:
        """
        Register moving image to fixed image using SimpleITK.
        
        Args:
            moving_image: Image to be registered
            fixed_image: Reference image
            
        Returns:
            Registered moving image
        """
        # Convert to numpy if needed
        if isinstance(moving_image, torch.Tensor):
            moving_image = moving_image.cpu().numpy()
        if isinstance(fixed_image, torch.Tensor):
            fixed_image = fixed_image.cpu().numpy()
            
        # Convert to SimpleITK images
        moving_sitk = sitk.GetImageFromArray(moving_image.astype(np.float32))
        fixed_sitk = sitk.GetImageFromArray(fixed_image.astype(np.float32))
        
        # Set up registration
        registration_method = sitk.ImageRegistrationMethod()
        
        # Similarity metric
        registration_method.SetMetricAsMeanSquares()
        
        # Interpolator
        registration_method.SetInterpolator(sitk.sitkLinear)
        
        # Optimizer
        registration_method.SetOptimizerAsGradientDescent(
            learningRate=1.0,
            numberOfIterations=100,
            convergenceMinimumValue=1e-6,
            convergenceWindowSize=10
        )
        
        # Setup for the multi-resolution framework
        registration_method.SetShrinkFactorsPerLevel(shrinkFactors=[4, 2, 1])
        registration_method.SetSmoothingSigmasPerLevel(smoothingSigmas=[2, 1, 0])
        registration_method.SmoothingSigmasAreSpecifiedInPhysicalUnitsOn()
        
        # Initial transform
        initial_transform = sitk.CenteredTransformInitializer(
            fixed_sitk,
            moving_sitk,
            sitk.Euler3DTransform(),
            sitk.CenteredTransformInitializerFilter.GEOMETRY
        )
        registration_method.SetInitialTransform(initial_transform, inPlace=False)
        
        try:
            # Execute registration
            final_transform = registration_method.Execute(fixed_sitk, moving_sitk)
            
            # Apply transform
            registered_image = sitk.Resample(
                moving_sitk,
                fixed_sitk,
                final_transform,
                sitk.sitkLinear,
                0.0,
                moving_sitk.GetPixelID()
            )
            
            registered_array = sitk.GetArrayFromImage(registered_image)
            logger.info("Image registration completed successfully")
            return registered_array
            
        except Exception as e:
            logger.warning(f"Image registration failed: {e}. Returning original moving image.")
            return moving_image
    
    def preprocess_image(
        self,
        image_path: str,
        apply_bias_correction: bool = True,
        apply_skull_stripping: bool = False,
        normalization_method: str = "z_score"
    ) -> torch.Tensor:
        """
        Complete preprocessing pipeline for a single image.
        
        Args:
            image_path: Path to input image
            apply_bias_correction: Whether to apply bias field correction
            apply_skull_stripping: Whether to apply skull stripping
            normalization_method: Intensity normalization method
            
        Returns:
            Preprocessed image tensor
        """
        # Load and apply basic transforms
        image = self.basic_transforms(image_path)
        
        # Convert to numpy for additional processing
        image_np = image.cpu().numpy()
        
        # Apply bias field correction
        if apply_bias_correction:
            image_np = self.bias_field_correction(image_np)
        
        # Apply skull stripping
        if apply_skull_stripping:
            image_np = self.skull_stripping(image_np)
        
        # Apply intensity normalization
        image_np = self.intensity_normalization(image_np, normalization_method)
        
        # Convert back to tensor
        processed_image = torch.from_numpy(image_np)
        
        # Apply final transforms (padding, resizing)
        if processed_image.ndim == 3:
            processed_image = processed_image.unsqueeze(0)  # Add channel dimension
            
        # Apply spatial transforms
        spatial_transforms = Compose([
            SpatialPad(
                spatial_size=self.config.preprocessing.image_size,
                mode="constant"
            ),
            Resize(
                spatial_size=self.config.preprocessing.image_size,
                mode="trilinear"
            )
        ])
        
        processed_image = spatial_transforms(processed_image)
        
        logger.info(f"Preprocessing completed for {image_path}")
        return processed_image
    
    def get_preprocessing_stats(self, image: torch.Tensor) -> Dict:
        """
        Get statistics of preprocessed image.
        
        Args:
            image: Preprocessed image tensor
            
        Returns:
            Dictionary with image statistics
        """
        image_np = image.cpu().numpy()
        
        stats = {
            "shape": image.shape,
            "dtype": str(image.dtype),
            "min": float(np.min(image_np)),
            "max": float(np.max(image_np)),
            "mean": float(np.mean(image_np)),
            "std": float(np.std(image_np)),
            "non_zero_voxels": int(np.count_nonzero(image_np))
        }
        
        return stats
