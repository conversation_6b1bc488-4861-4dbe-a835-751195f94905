"""
Document parser for various medical document formats.
"""

import logging
from typing import Dict, List, Optional, Any
from pathlib import Path

logger = logging.getLogger(__name__)


class DocumentParser:
    """Parser for medical documents."""
    
    def __init__(self, config):
        """Initialize document parser."""
        self.config = config
        
    def parse_document(self, file_path: str) -> Dict[str, Any]:
        """Parse a document file."""
        file_path = Path(file_path)
        
        if file_path.suffix.lower() == '.pdf':
            return self._parse_pdf(file_path)
        elif file_path.suffix.lower() == '.txt':
            return self._parse_text(file_path)
        else:
            return {"content": "", "metadata": {}}
    
    def _parse_pdf(self, file_path: Path) -> Dict[str, Any]:
        """Parse PDF document."""
        try:
            # Simple text extraction - in production use proper PDF parsing
            with open(file_path, 'rb') as f:
                content = f"PDF content from {file_path.name}"
            
            return {
                "content": content,
                "metadata": {"type": "pdf", "filename": file_path.name}
            }
        except Exception as e:
            logger.error(f"Failed to parse PDF {file_path}: {e}")
            return {"content": "", "metadata": {}}
    
    def _parse_text(self, file_path: Path) -> Dict[str, Any]:
        """Parse text document."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            return {
                "content": content,
                "metadata": {"type": "text", "filename": file_path.name}
            }
        except Exception as e:
            logger.error(f"Failed to parse text file {file_path}: {e}")
            return {"content": "", "metadata": {}}
