"""
Logging utilities for Healthcare Triage Agent.
"""

import os
import logging
import logging.handlers
from pathlib import Path
from typing import Optional
from datetime import datetime

from .config import Config


def setup_logging(config: Optional[Config] = None, log_level: Optional[str] = None) -> logging.Logger:
    """
    Set up comprehensive logging for the application.
    
    Args:
        config: Configuration object (optional)
        log_level: Override log level (optional)
        
    Returns:
        Configured logger
    """
    # Use config or defaults
    if config:
        level = log_level or config.logging.level
        log_format = config.logging.format
        log_file = config.logging.file
    else:
        level = log_level or "INFO"
        log_format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        log_file = "logs/healthcare_triage.log"
    
    # Create logs directory
    log_dir = Path(log_file).parent
    log_dir.mkdir(parents=True, exist_ok=True)
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, level.upper()))
    
    # Clear existing handlers
    root_logger.handlers.clear()
    
    # Create formatter
    formatter = logging.Formatter(log_format)
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(getattr(logging, level.upper()))
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)
    
    # File handler with rotation
    file_handler = logging.handlers.RotatingFileHandler(
        log_file,
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5
    )
    file_handler.setLevel(getattr(logging, level.upper()))
    file_handler.setFormatter(formatter)
    root_logger.addHandler(file_handler)
    
    # HIPAA audit log handler (if compliance enabled)
    if config and config.security.audit_logging:
        audit_log_file = log_dir / "hipaa_audit.log"
        audit_handler = logging.handlers.RotatingFileHandler(
            audit_log_file,
            maxBytes=50*1024*1024,  # 50MB
            backupCount=10
        )
        audit_formatter = logging.Formatter(
            "%(asctime)s - AUDIT - %(name)s - %(levelname)s - %(message)s"
        )
        audit_handler.setFormatter(audit_formatter)
        
        # Create audit logger
        audit_logger = logging.getLogger("healthcare_triage.audit")
        audit_logger.addHandler(audit_handler)
        audit_logger.setLevel(logging.INFO)
    
    # Get main application logger
    logger = logging.getLogger("healthcare_triage")
    logger.info("Logging system initialized")
    
    return logger


def get_logger(name: str) -> logging.Logger:
    """
    Get a logger with the specified name.
    
    Args:
        name: Logger name
        
    Returns:
        Logger instance
    """
    return logging.getLogger(f"healthcare_triage.{name}")


def log_patient_access(patient_id: str, user_id: str, action: str, details: Optional[str] = None):
    """
    Log patient data access for HIPAA compliance.
    
    Args:
        patient_id: Patient identifier
        user_id: User accessing the data
        action: Action performed
        details: Additional details
    """
    audit_logger = logging.getLogger("healthcare_triage.audit")
    
    log_message = f"PATIENT_ACCESS - Patient: {patient_id} - User: {user_id} - Action: {action}"
    if details:
        log_message += f" - Details: {details}"
    
    audit_logger.info(log_message)


def log_model_inference(model_name: str, input_data: str, output_data: str, confidence: Optional[float] = None):
    """
    Log model inference for audit trail.
    
    Args:
        model_name: Name of the model
        input_data: Description of input data
        output_data: Description of output
        confidence: Model confidence score
    """
    audit_logger = logging.getLogger("healthcare_triage.audit")
    
    log_message = f"MODEL_INFERENCE - Model: {model_name} - Input: {input_data} - Output: {output_data}"
    if confidence is not None:
        log_message += f" - Confidence: {confidence:.3f}"
    
    audit_logger.info(log_message)


def log_triage_decision(patient_id: str, priority_level: str, reasoning: str, user_id: Optional[str] = None):
    """
    Log triage decisions for audit trail.
    
    Args:
        patient_id: Patient identifier
        priority_level: Assigned priority level
        reasoning: Reasoning for the decision
        user_id: User making or reviewing the decision
    """
    audit_logger = logging.getLogger("healthcare_triage.audit")
    
    log_message = f"TRIAGE_DECISION - Patient: {patient_id} - Priority: {priority_level} - Reasoning: {reasoning}"
    if user_id:
        log_message += f" - User: {user_id}"
    
    audit_logger.info(log_message)


class PerformanceLogger:
    """Logger for performance metrics and monitoring."""
    
    def __init__(self):
        self.logger = get_logger("performance")
        self.start_times = {}
    
    def start_timer(self, operation: str):
        """Start timing an operation."""
        self.start_times[operation] = datetime.now()
    
    def end_timer(self, operation: str, additional_info: Optional[str] = None):
        """End timing an operation and log the duration."""
        if operation in self.start_times:
            duration = datetime.now() - self.start_times[operation]
            duration_ms = duration.total_seconds() * 1000
            
            log_message = f"PERFORMANCE - {operation}: {duration_ms:.2f}ms"
            if additional_info:
                log_message += f" - {additional_info}"
            
            self.logger.info(log_message)
            del self.start_times[operation]
        else:
            self.logger.warning(f"Timer for operation '{operation}' was not started")
    
    def log_memory_usage(self, operation: str):
        """Log current memory usage."""
        try:
            import psutil
            process = psutil.Process()
            memory_mb = process.memory_info().rss / 1024 / 1024
            self.logger.info(f"MEMORY - {operation}: {memory_mb:.2f}MB")
        except ImportError:
            self.logger.warning("psutil not available for memory monitoring")
    
    def log_gpu_usage(self, operation: str):
        """Log GPU memory usage if available."""
        try:
            import torch
            if torch.cuda.is_available():
                gpu_memory_mb = torch.cuda.memory_allocated() / 1024 / 1024
                gpu_memory_cached_mb = torch.cuda.memory_reserved() / 1024 / 1024
                self.logger.info(
                    f"GPU_MEMORY - {operation}: Allocated: {gpu_memory_mb:.2f}MB, "
                    f"Cached: {gpu_memory_cached_mb:.2f}MB"
                )
        except ImportError:
            pass


class SecurityLogger:
    """Logger for security events and compliance."""
    
    def __init__(self):
        self.logger = get_logger("security")
    
    def log_authentication(self, user_id: str, success: bool, ip_address: Optional[str] = None):
        """Log authentication attempts."""
        status = "SUCCESS" if success else "FAILURE"
        log_message = f"AUTH_{status} - User: {user_id}"
        if ip_address:
            log_message += f" - IP: {ip_address}"
        
        if success:
            self.logger.info(log_message)
        else:
            self.logger.warning(log_message)
    
    def log_data_access(self, user_id: str, resource: str, action: str, authorized: bool):
        """Log data access attempts."""
        status = "AUTHORIZED" if authorized else "UNAUTHORIZED"
        log_message = f"DATA_ACCESS_{status} - User: {user_id} - Resource: {resource} - Action: {action}"
        
        if authorized:
            self.logger.info(log_message)
        else:
            self.logger.warning(log_message)
    
    def log_encryption_event(self, operation: str, success: bool, details: Optional[str] = None):
        """Log encryption/decryption events."""
        status = "SUCCESS" if success else "FAILURE"
        log_message = f"ENCRYPTION_{status} - Operation: {operation}"
        if details:
            log_message += f" - Details: {details}"
        
        if success:
            self.logger.info(log_message)
        else:
            self.logger.error(log_message)


def configure_third_party_loggers():
    """Configure logging levels for third-party libraries."""
    # Reduce verbosity of common third-party libraries
    logging.getLogger("transformers").setLevel(logging.WARNING)
    logging.getLogger("torch").setLevel(logging.WARNING)
    logging.getLogger("monai").setLevel(logging.WARNING)
    logging.getLogger("langchain").setLevel(logging.INFO)
    logging.getLogger("openai").setLevel(logging.WARNING)
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    logging.getLogger("requests").setLevel(logging.WARNING)


# Initialize performance and security loggers as singletons
performance_logger = PerformanceLogger()
security_logger = SecurityLogger()


def setup_comprehensive_logging(config: Optional[Config] = None) -> logging.Logger:
    """
    Set up comprehensive logging with all components.
    
    Args:
        config: Configuration object
        
    Returns:
        Main application logger
    """
    # Set up main logging
    logger = setup_logging(config)
    
    # Configure third-party loggers
    configure_third_party_loggers()
    
    # Log system information
    logger.info("Healthcare Multimodal Triage Agent - Logging System Initialized")
    logger.info(f"Python version: {os.sys.version}")
    logger.info(f"Working directory: {os.getcwd()}")
    
    # Log compliance status
    if config and config.security.hipaa_compliance:
        logger.info("HIPAA compliance mode enabled - Audit logging active")
    
    return logger
