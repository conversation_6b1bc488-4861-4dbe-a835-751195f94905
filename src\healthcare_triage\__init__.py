"""
Healthcare Multimodal Triage Agent

A comprehensive AI system that combines brain MRI segmentation using PaliGemma 2 
with LLM-based patient history analysis for intelligent healthcare triage.
"""

__version__ = "0.1.0"
__author__ = "Healthcare AI Team"
__email__ = "<EMAIL>"

from .imaging import MRIProcessor, PaliGemmaSegmenter
from .nlp import PatientHistoryProcessor, DocumentParser
from .agents import TriageAgent, MedicalKnowledgeAgent
from .triage import TriageDecisionEngine, RiskAssessment

__all__ = [
    "MRIProcessor",
    "PaliGemmaSegmenter", 
    "PatientHistoryProcessor",
    "DocumentParser",
    "TriageAgent",
    "MedicalKnowledgeAgent",
    "TriageDecisionEngine",
    "RiskAssessment",
]
