"""
Post-processing utilities for segmentation results.
"""

import logging
from typing import Dict, List, Optional, Tuple, Union

import torch
import numpy as np
from scipy import ndimage
from skimage import morphology, measure
import cv2

logger = logging.getLogger(__name__)


class SegmentationPostProcessor:
    """
    Post-processing pipeline for segmentation results.
    
    Provides:
    - Morphological operations
    - Connected component analysis
    - Hole filling
    - Smoothing
    - Size filtering
    """
    
    def __init__(self, min_component_size: int = 100):
        """
        Initialize post-processor.
        
        Args:
            min_component_size: Minimum size for connected components
        """
        self.min_component_size = min_component_size
        
    def remove_small_components(
        self,
        segmentation: Union[torch.Tensor, np.ndarray],
        min_size: Optional[int] = None
    ) -> np.ndarray:
        """
        Remove small connected components.
        
        Args:
            segmentation: Binary segmentation mask
            min_size: Minimum component size (uses default if None)
            
        Returns:
            Cleaned segmentation mask
        """
        if isinstance(segmentation, torch.Tensor):
            segmentation = segmentation.cpu().numpy()
            
        if min_size is None:
            min_size = self.min_component_size
            
        # Handle different dimensions
        if segmentation.ndim == 4:  # Batch dimension
            segmentation = segmentation[0]
        if segmentation.ndim == 3 and segmentation.shape[0] == 1:  # Channel dimension
            segmentation = segmentation[0]
            
        # Convert to binary if needed
        if segmentation.dtype != bool:
            segmentation = segmentation > 0.5
            
        # Remove small components
        if segmentation.ndim == 3:
            # 3D case
            labeled_array, num_features = ndimage.label(segmentation)
            component_sizes = ndimage.sum(segmentation, labeled_array, range(num_features + 1))
            
            # Create mask for components to keep
            mask_sizes = component_sizes >= min_size
            remove_pixel = mask_sizes[labeled_array]
            segmentation[~remove_pixel] = 0
            
        else:
            # 2D case
            segmentation = morphology.remove_small_objects(
                segmentation.astype(bool),
                min_size=min_size
            )
            
        logger.info(f"Removed small components (min_size={min_size})")
        return segmentation.astype(np.uint8)
    
    def fill_holes(self, segmentation: Union[torch.Tensor, np.ndarray]) -> np.ndarray:
        """
        Fill holes in segmentation mask.
        
        Args:
            segmentation: Binary segmentation mask
            
        Returns:
            Hole-filled segmentation mask
        """
        if isinstance(segmentation, torch.Tensor):
            segmentation = segmentation.cpu().numpy()
            
        # Handle dimensions
        if segmentation.ndim == 4:
            segmentation = segmentation[0]
        if segmentation.ndim == 3 and segmentation.shape[0] == 1:
            segmentation = segmentation[0]
            
        # Convert to binary
        binary_mask = segmentation > 0.5
        
        if segmentation.ndim == 3:
            # 3D hole filling
            filled = ndimage.binary_fill_holes(binary_mask)
        else:
            # 2D hole filling
            filled = ndimage.binary_fill_holes(binary_mask)
            
        logger.info("Holes filled in segmentation")
        return filled.astype(np.uint8)
    
    def morphological_operations(
        self,
        segmentation: Union[torch.Tensor, np.ndarray],
        operation: str = "closing",
        kernel_size: int = 3,
        iterations: int = 1
    ) -> np.ndarray:
        """
        Apply morphological operations.
        
        Args:
            segmentation: Binary segmentation mask
            operation: Type of operation ("opening", "closing", "erosion", "dilation")
            kernel_size: Size of morphological kernel
            iterations: Number of iterations
            
        Returns:
            Processed segmentation mask
        """
        if isinstance(segmentation, torch.Tensor):
            segmentation = segmentation.cpu().numpy()
            
        # Handle dimensions
        if segmentation.ndim == 4:
            segmentation = segmentation[0]
        if segmentation.ndim == 3 and segmentation.shape[0] == 1:
            segmentation = segmentation[0]
            
        # Convert to binary
        binary_mask = (segmentation > 0.5).astype(np.uint8)
        
        # Create kernel
        if segmentation.ndim == 3:
            kernel = np.ones((kernel_size, kernel_size, kernel_size), np.uint8)
        else:
            kernel = np.ones((kernel_size, kernel_size), np.uint8)
            
        # Apply operation
        if operation == "erosion":
            if segmentation.ndim == 3:
                result = ndimage.binary_erosion(binary_mask, kernel, iterations=iterations)
            else:
                result = cv2.erode(binary_mask, kernel, iterations=iterations)
                
        elif operation == "dilation":
            if segmentation.ndim == 3:
                result = ndimage.binary_dilation(binary_mask, kernel, iterations=iterations)
            else:
                result = cv2.dilate(binary_mask, kernel, iterations=iterations)
                
        elif operation == "opening":
            if segmentation.ndim == 3:
                result = ndimage.binary_opening(binary_mask, kernel, iterations=iterations)
            else:
                result = cv2.morphologyEx(binary_mask, cv2.MORPH_OPEN, kernel)
                
        elif operation == "closing":
            if segmentation.ndim == 3:
                result = ndimage.binary_closing(binary_mask, kernel, iterations=iterations)
            else:
                result = cv2.morphologyEx(binary_mask, cv2.MORPH_CLOSE, kernel)
                
        else:
            raise ValueError(f"Unknown morphological operation: {operation}")
            
        logger.info(f"Applied {operation} with kernel size {kernel_size}")
        return result.astype(np.uint8)
    
    def smooth_contours(
        self,
        segmentation: Union[torch.Tensor, np.ndarray],
        sigma: float = 1.0
    ) -> np.ndarray:
        """
        Smooth segmentation contours using Gaussian filtering.
        
        Args:
            segmentation: Segmentation mask
            sigma: Standard deviation for Gaussian kernel
            
        Returns:
            Smoothed segmentation mask
        """
        if isinstance(segmentation, torch.Tensor):
            segmentation = segmentation.cpu().numpy()
            
        # Handle dimensions
        if segmentation.ndim == 4:
            segmentation = segmentation[0]
        if segmentation.ndim == 3 and segmentation.shape[0] == 1:
            segmentation = segmentation[0]
            
        # Apply Gaussian smoothing
        smoothed = ndimage.gaussian_filter(segmentation.astype(np.float32), sigma=sigma)
        
        # Threshold back to binary
        smoothed_binary = (smoothed > 0.5).astype(np.uint8)
        
        logger.info(f"Contours smoothed with sigma={sigma}")
        return smoothed_binary
    
    def get_largest_component(self, segmentation: Union[torch.Tensor, np.ndarray]) -> np.ndarray:
        """
        Keep only the largest connected component.
        
        Args:
            segmentation: Binary segmentation mask
            
        Returns:
            Segmentation with only largest component
        """
        if isinstance(segmentation, torch.Tensor):
            segmentation = segmentation.cpu().numpy()
            
        # Handle dimensions
        if segmentation.ndim == 4:
            segmentation = segmentation[0]
        if segmentation.ndim == 3 and segmentation.shape[0] == 1:
            segmentation = segmentation[0]
            
        # Convert to binary
        binary_mask = segmentation > 0.5
        
        if segmentation.ndim == 3:
            # 3D case
            labeled_array, num_features = ndimage.label(binary_mask)
            if num_features == 0:
                return binary_mask.astype(np.uint8)
                
            component_sizes = ndimage.sum(binary_mask, labeled_array, range(1, num_features + 1))
            largest_component_label = np.argmax(component_sizes) + 1
            largest_component = labeled_array == largest_component_label
            
        else:
            # 2D case
            labeled_array = measure.label(binary_mask)
            if labeled_array.max() == 0:
                return binary_mask.astype(np.uint8)
                
            component_sizes = [np.sum(labeled_array == i) for i in range(1, labeled_array.max() + 1)]
            largest_component_label = np.argmax(component_sizes) + 1
            largest_component = labeled_array == largest_component_label
            
        logger.info("Kept only largest connected component")
        return largest_component.astype(np.uint8)
    
    def apply_crf_postprocessing(
        self,
        segmentation: Union[torch.Tensor, np.ndarray],
        original_image: Union[torch.Tensor, np.ndarray],
        num_iterations: int = 10
    ) -> np.ndarray:
        """
        Apply Conditional Random Field (CRF) post-processing.
        Note: This is a placeholder implementation. For full CRF, use pydensecrf.
        
        Args:
            segmentation: Segmentation probabilities
            original_image: Original image
            num_iterations: Number of CRF iterations
            
        Returns:
            CRF-refined segmentation
        """
        # This is a simplified implementation
        # For full CRF implementation, you would use pydensecrf library
        
        if isinstance(segmentation, torch.Tensor):
            segmentation = segmentation.cpu().numpy()
        if isinstance(original_image, torch.Tensor):
            original_image = original_image.cpu().numpy()
            
        # Simple bilateral filtering as approximation
        if segmentation.ndim == 2:
            refined = cv2.bilateralFilter(
                segmentation.astype(np.float32),
                d=9,
                sigmaColor=75,
                sigmaSpace=75
            )
        else:
            # For 3D, apply slice by slice
            refined = np.zeros_like(segmentation)
            for i in range(segmentation.shape[0]):
                refined[i] = cv2.bilateralFilter(
                    segmentation[i].astype(np.float32),
                    d=9,
                    sigmaColor=75,
                    sigmaSpace=75
                )
        
        logger.info("Applied CRF-like post-processing")
        return (refined > 0.5).astype(np.uint8)
    
    def complete_postprocessing_pipeline(
        self,
        segmentation: Union[torch.Tensor, np.ndarray],
        original_image: Optional[Union[torch.Tensor, np.ndarray]] = None,
        fill_holes: bool = True,
        remove_small_objects: bool = True,
        morphological_closing: bool = True,
        smooth_contours: bool = True,
        keep_largest_only: bool = False
    ) -> np.ndarray:
        """
        Apply complete post-processing pipeline.
        
        Args:
            segmentation: Raw segmentation output
            original_image: Original image (for CRF if needed)
            fill_holes: Whether to fill holes
            remove_small_objects: Whether to remove small objects
            morphological_closing: Whether to apply morphological closing
            smooth_contours: Whether to smooth contours
            keep_largest_only: Whether to keep only largest component
            
        Returns:
            Post-processed segmentation
        """
        result = segmentation.copy() if isinstance(segmentation, np.ndarray) else segmentation.clone()
        
        # Fill holes
        if fill_holes:
            result = self.fill_holes(result)
            
        # Remove small objects
        if remove_small_objects:
            result = self.remove_small_components(result)
            
        # Morphological closing
        if morphological_closing:
            result = self.morphological_operations(result, operation="closing", kernel_size=3)
            
        # Smooth contours
        if smooth_contours:
            result = self.smooth_contours(result, sigma=1.0)
            
        # Keep largest component only
        if keep_largest_only:
            result = self.get_largest_component(result)
            
        logger.info("Complete post-processing pipeline applied")
        return result
    
    def calculate_segmentation_metrics(
        self,
        prediction: Union[torch.Tensor, np.ndarray],
        ground_truth: Union[torch.Tensor, np.ndarray]
    ) -> Dict[str, float]:
        """
        Calculate segmentation evaluation metrics.
        
        Args:
            prediction: Predicted segmentation
            ground_truth: Ground truth segmentation
            
        Returns:
            Dictionary with evaluation metrics
        """
        if isinstance(prediction, torch.Tensor):
            prediction = prediction.cpu().numpy()
        if isinstance(ground_truth, torch.Tensor):
            ground_truth = ground_truth.cpu().numpy()
            
        # Convert to binary
        pred_binary = (prediction > 0.5).astype(bool)
        gt_binary = (ground_truth > 0.5).astype(bool)
        
        # Calculate metrics
        intersection = np.logical_and(pred_binary, gt_binary).sum()
        union = np.logical_or(pred_binary, gt_binary).sum()
        pred_sum = pred_binary.sum()
        gt_sum = gt_binary.sum()
        
        # Dice coefficient
        dice = (2.0 * intersection) / (pred_sum + gt_sum + 1e-8)
        
        # IoU (Jaccard index)
        iou = intersection / (union + 1e-8)
        
        # Sensitivity (Recall)
        sensitivity = intersection / (gt_sum + 1e-8)
        
        # Specificity
        true_negatives = np.logical_and(~pred_binary, ~gt_binary).sum()
        false_positives = np.logical_and(pred_binary, ~gt_binary).sum()
        specificity = true_negatives / (true_negatives + false_positives + 1e-8)
        
        # Precision
        precision = intersection / (pred_sum + 1e-8)
        
        metrics = {
            "dice_score": float(dice),
            "iou": float(iou),
            "sensitivity": float(sensitivity),
            "specificity": float(specificity),
            "precision": float(precision)
        }
        
        return metrics
