"""
MRI Processor for handling brain MRI data preprocessing and loading.
"""

import os
import logging
from typing import Dict, List, Optional, Tuple, Union
from pathlib import Path

import torch
import numpy as np
import nibabel as nib
from monai.data import Dataset, DataLoader
from monai.transforms import (
    Compose, LoadImaged, EnsureChannelFirstd, Orientationd,
    Spacingd, ScaleIntensityRanged, CropForegroundd,
    RandCropByPosNegLabeld, RandFlipd, RandRotate90d,
    RandShiftIntensityd, ToTensord
)

from ..utils.config import Config
from .preprocessing import MRIPreprocessor
from .augmentation import MRIAugmentation

logger = logging.getLogger(__name__)


class MRIProcessor:
    """
    Main class for processing brain MRI data for tumor segmentation.
    
    This class handles:
    - Loading MRI data from various formats (NIfTI, DICOM)
    - Preprocessing using MONAI transforms
    - Data augmentation for training
    - Batch preparation for model training/inference
    """
    
    def __init__(self, config: Config):
        """
        Initialize MRI processor with configuration.
        
        Args:
            config: Configuration object containing processing parameters
        """
        self.config = config
        self.preprocessor = MRIPreprocessor(config)
        self.augmentation = MRIAugmentation(config)
        
        # Set up transforms
        self._setup_transforms()
        
    def _setup_transforms(self):
        """Set up MONAI transforms for preprocessing."""
        
        # Base transforms for all data
        self.base_transforms = Compose([
            LoadImaged(keys=["image", "label"]),
            EnsureChannelFirstd(keys=["image", "label"]),
            Orientationd(keys=["image", "label"], axcodes="RAS"),
            Spacingd(
                keys=["image", "label"],
                pixdim=self.config.preprocessing.spacing,
                mode=("bilinear", "nearest")
            ),
            ScaleIntensityRanged(
                keys=["image"],
                a_min=0, a_max=1000,
                b_min=0.0, b_max=1.0,
                clip=True
            ),
            CropForegroundd(keys=["image", "label"], source_key="image"),
        ])
        
        # Training transforms with augmentation
        self.train_transforms = Compose([
            self.base_transforms,
            RandCropByPosNegLabeld(
                keys=["image", "label"],
                label_key="label",
                spatial_size=self.config.preprocessing.image_size,
                pos=1, neg=1,
                num_samples=4,
                image_key="image",
                image_threshold=0
            ),
            RandFlipd(
                keys=["image", "label"],
                spatial_axis=[0],
                prob=self.config.preprocessing.augmentation.flip_probability
            ),
            RandRotate90d(
                keys=["image", "label"],
                prob=0.1,
                max_k=3
            ),
            RandShiftIntensityd(
                keys=["image"],
                offsets=0.10,
                prob=0.50
            ),
            ToTensord(keys=["image", "label"])
        ])
        
        # Validation/inference transforms
        self.val_transforms = Compose([
            self.base_transforms,
            ToTensord(keys=["image", "label"])
        ])
        
    def load_dataset(
        self, 
        data_dir: Union[str, Path],
        split: str = "train"
    ) -> Dataset:
        """
        Load MRI dataset from directory.
        
        Args:
            data_dir: Path to data directory
            split: Dataset split ("train", "val", "test")
            
        Returns:
            MONAI Dataset object
        """
        data_dir = Path(data_dir)
        
        # Find all MRI files
        image_files = list(data_dir.glob("**/images/*.nii.gz"))
        label_files = list(data_dir.glob("**/labels/*.nii.gz"))
        
        # Create data dictionaries
        data_dicts = []
        for img_file in image_files:
            # Find corresponding label file
            label_file = None
            for lbl_file in label_files:
                if img_file.stem.replace("_image", "") == lbl_file.stem.replace("_label", ""):
                    label_file = lbl_file
                    break
                    
            if label_file:
                data_dicts.append({
                    "image": str(img_file),
                    "label": str(label_file)
                })
        
        logger.info(f"Loaded {len(data_dicts)} samples for {split} split")
        
        # Choose transforms based on split
        transforms = self.train_transforms if split == "train" else self.val_transforms
        
        return Dataset(data=data_dicts, transform=transforms)
    
    def create_dataloader(
        self,
        dataset: Dataset,
        batch_size: Optional[int] = None,
        shuffle: bool = True,
        num_workers: int = 4
    ) -> DataLoader:
        """
        Create DataLoader from dataset.
        
        Args:
            dataset: MONAI Dataset
            batch_size: Batch size (uses config default if None)
            shuffle: Whether to shuffle data
            num_workers: Number of worker processes
            
        Returns:
            DataLoader object
        """
        if batch_size is None:
            batch_size = self.config.models.paligemma.batch_size
            
        return DataLoader(
            dataset,
            batch_size=batch_size,
            shuffle=shuffle,
            num_workers=num_workers,
            pin_memory=torch.cuda.is_available()
        )
    
    def preprocess_single_image(
        self, 
        image_path: Union[str, Path]
    ) -> torch.Tensor:
        """
        Preprocess a single MRI image for inference.
        
        Args:
            image_path: Path to MRI image file
            
        Returns:
            Preprocessed image tensor
        """
        # Create temporary data dict
        data_dict = {"image": str(image_path)}
        
        # Apply preprocessing transforms (without label)
        preprocess_transforms = Compose([
            LoadImaged(keys=["image"]),
            EnsureChannelFirstd(keys=["image"]),
            Orientationd(keys=["image"], axcodes="RAS"),
            Spacingd(
                keys=["image"],
                pixdim=self.config.preprocessing.spacing,
                mode="bilinear"
            ),
            ScaleIntensityRanged(
                keys=["image"],
                a_min=0, a_max=1000,
                b_min=0.0, b_max=1.0,
                clip=True
            ),
            CropForegroundd(keys=["image"], source_key="image"),
            ToTensord(keys=["image"])
        ])
        
        processed = preprocess_transforms(data_dict)
        return processed["image"]
    
    def save_segmentation(
        self,
        segmentation: torch.Tensor,
        original_image_path: Union[str, Path],
        output_path: Union[str, Path]
    ):
        """
        Save segmentation result as NIfTI file.
        
        Args:
            segmentation: Segmentation tensor
            original_image_path: Path to original image for header info
            output_path: Output path for segmentation
        """
        # Load original image to get header information
        original_img = nib.load(str(original_image_path))
        
        # Convert tensor to numpy
        if isinstance(segmentation, torch.Tensor):
            seg_array = segmentation.cpu().numpy()
        else:
            seg_array = segmentation
            
        # Create new NIfTI image with original header
        seg_img = nib.Nifti1Image(
            seg_array.astype(np.uint8),
            original_img.affine,
            original_img.header
        )
        
        # Save segmentation
        nib.save(seg_img, str(output_path))
        logger.info(f"Segmentation saved to {output_path}")
    
    def get_dataset_statistics(self, dataset: Dataset) -> Dict:
        """
        Calculate dataset statistics for normalization.
        
        Args:
            dataset: MONAI Dataset
            
        Returns:
            Dictionary containing dataset statistics
        """
        intensities = []
        
        for data in dataset:
            image = data["image"]
            intensities.extend(image.flatten().tolist())
        
        intensities = np.array(intensities)
        
        stats = {
            "mean": float(np.mean(intensities)),
            "std": float(np.std(intensities)),
            "min": float(np.min(intensities)),
            "max": float(np.max(intensities)),
            "median": float(np.median(intensities))
        }
        
        logger.info(f"Dataset statistics: {stats}")
        return stats
