# Healthcare Multimodal Triage Agent

A comprehensive AI system that combines brain MRI segmentation using PaliGemma 2 with LLM-based patient history analysis for intelligent healthcare triage.

## 🚀 Quick Start

```bash
# 1. Clone and setup
git clone <repository-url>
cd healthcare-triage-agent
python scripts/setup_environment.py

# 2. Download sample data
python scripts/download_sample_data.py --all

# 3. Run complete demo
python scripts/demo.py

# 4. Train model (with real data)
python scripts/train_model.py

# 5. Run inference
python scripts/run_inference.py --mri-image data/mri/test/images/sample.nii.gz --patient-id patient_001
```

## 📋 Project Overview

This project implements an end-to-end healthcare AI system that:
- 🧠 **Brain MRI Analysis**: Tumor segmentation using fine-tuned PaliGemma 2
- 📄 **Patient History Analysis**: LLM agents built with LangChain for document processing
- 🏥 **Intelligent Triage**: Priority assessment and clinical decision support
- 🔍 **Explainable AI**: Clear reasoning for all recommendations

## 🏗️ System Architecture

```
Healthcare Multimodal Triage Agent
├── 🖼️  Brain MRI Analysis (PaliGemma 2)
│   ├── Image Preprocessing (MONAI)
│   ├── Tumor Segmentation
│   └── Feature Extraction
├── 📝 Patient History Analysis (LLM + LangChain)
│   ├── Document Processing
│   ├── Text Summarization
│   └── Knowledge Retrieval (RAG)
└── 🚨 Triage Decision Engine
    ├── Multimodal Fusion
    ├── Risk Assessment
    └── Recommendation Generation
```

## 📁 Project Structure

```
healthcare-triage-agent/
├── src/healthcare_triage/    # Core implementation
│   ├── imaging/              # Brain MRI processing
│   ├── nlp/                  # Patient history analysis
│   ├── agents/               # LangChain agents
│   ├── triage/               # Decision engine
│   └── utils/                # Shared utilities
├── data/                     # Datasets and knowledge base
│   ├── mri/                  # MRI datasets (train/val/test)
│   ├── patient_records/      # Patient history data
│   └── knowledge_base/       # Medical knowledge base
├── models/                   # Model storage
│   ├── paligemma/           # PaliGemma 2 models
│   └── checkpoints/         # Training checkpoints
├── scripts/                  # Automation scripts
│   ├── setup_environment.py # Environment setup
│   ├── download_sample_data.py # Data preparation
│   ├── train_model.py       # Model training
│   ├── run_inference.py     # Inference pipeline
│   └── demo.py              # Complete demonstration
├── configs/                  # Configuration files
├── tests/                    # Unit and integration tests
├── notebooks/                # Jupyter notebooks
└── docs/                     # Documentation
```

## 🛠️ Installation

### Prerequisites
- Python 3.9+
- CUDA-capable GPU (recommended)
- 16GB+ RAM
- 50GB+ storage space

### Step 1: Environment Setup
```bash
# Clone repository
git clone <repository-url>
cd healthcare-triage-agent

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
python setup.py install
```

### Step 2: Configuration
```bash
# Run setup script
python scripts/setup_environment.py

# Update API keys in .env file
cp .env.example .env
# Edit .env with your API keys
```

### Step 3: Data Preparation
```bash
# Download sample datasets
python scripts/download_sample_data.py --all

# For real datasets, follow data acquisition guidelines
python scripts/download_sample_data.py --list
```

## 🎯 Usage

### Complete Demo
```bash
# Run full system demonstration
python scripts/demo.py
```

### Model Training
```bash
# Train PaliGemma 2 for brain MRI segmentation
python scripts/train_model.py

# With custom configuration
python scripts/train_model.py --config configs/custom_config.yaml

# Evaluation only
python scripts/train_model.py --eval-only --model-path models/paligemma/fine_tuned
```

### Inference Pipeline
```bash
# Single patient triage assessment
python scripts/run_inference.py \
    --mri-image data/mri/test/images/patient_001_image.nii.gz \
    --patient-id patient_001 \
    --save-report \
    --visualize

# With custom model
python scripts/run_inference.py \
    --mri-image path/to/mri.nii.gz \
    --patient-id patient_123 \
    --model-path models/paligemma/custom_model \
    --output-dir outputs/custom_run
```

### Python API Usage
```python
from healthcare_triage import MRIProcessor, TriageAgent
from healthcare_triage.utils.config import load_config

# Initialize components
config = load_config()
mri_processor = MRIProcessor(config)
triage_agent = TriageAgent(config)

# Process MRI image
processed_image = mri_processor.preprocess_single_image("path/to/mri.nii.gz")

# Load patient data
patient_data = load_patient_data("patient_id")

# Perform triage assessment
triage_result = triage_agent.perform_triage(patient_data, mri_results)
print(f"Priority: {triage_result['priority_level']}")
```

## 🔧 Configuration

### Main Configuration (`configs/config.yaml`)
```yaml
# Model settings
models:
  paligemma:
    model_name: "google/paligemma2-3b-pt-224"
    batch_size: 4
    learning_rate: 1e-5
    num_epochs: 10

# Data paths
data:
  mri_data_path: "data/mri"
  patient_records_path: "data/patient_records"
  knowledge_base_path: "data/knowledge_base"

# Agent settings
agents:
  triage_agent:
    model: "gpt-4"
    temperature: 0.1
    max_iterations: 5
```

### Environment Variables (`.env`)
```bash
# API Keys
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Paths
PROJECT_ROOT=.
DATA_PATH=./data
MODELS_PATH=./models

# Security
ENCRYPTION_KEY=your_encryption_key_here
HIPAA_AUDIT_LOG=./logs/hipaa_audit.log
```

## 📊 Development Phases

### Phase 1: Foundational Development (6-12 Months) ✅
- [x] Project structure and environment setup
- [x] MRI preprocessing pipeline (MONAI)
- [x] PaliGemma 2 integration
- [x] Patient data processing
- [x] Synthetic data generation

### Phase 2: LLM Agent Development (9-15 Months) 🔄
- [x] LangChain agent framework
- [x] Triage decision agent
- [ ] Medical knowledge RAG system
- [ ] Multi-agent coordination
- [ ] Clinical validation pipeline

### Phase 3: Clinical Validation (12-24 Months) 📋
- [ ] Clinical testing protocols
- [ ] Regulatory compliance (FDA)
- [ ] Performance validation
- [ ] Deployment preparation
- [ ] User interface development

## 🧪 Testing

```bash
# Run all tests
python -m pytest tests/

# Run specific test categories
python -m pytest tests/test_imaging.py
python -m pytest tests/test_agents.py

# Run with coverage
python -m pytest --cov=healthcare_triage tests/
```

## 📈 Performance Metrics

### Target Performance (Clinical Validation)
- **Segmentation Accuracy**: >95% Dice score
- **Triage Accuracy**: >90% agreement with expert clinicians
- **Processing Time**: <5 minutes per case
- **System Uptime**: >99.9%

### Current Status (Development)
- ✅ Environment setup and basic pipeline
- ✅ Synthetic data generation
- ✅ Model integration framework
- 🔄 Training pipeline implementation
- 📋 Clinical validation pending

## 🔒 Compliance and Security

### HIPAA Compliance
- ✅ Audit logging for all patient data access
- ✅ Data encryption at rest and in transit
- ✅ Access controls and authentication
- ✅ De-identification protocols

### GDPR Compliance
- ✅ Data minimization principles
- ✅ Right to erasure implementation
- ✅ Consent management
- ✅ Privacy by design

### FDA Regulatory Pathway
- 📋 Software as Medical Device (SaMD) classification
- 📋 Clinical validation protocols
- 📋 Quality management system
- 📋 Risk management documentation

## 🤝 Contributing

We welcome contributions! Please see [CONTRIBUTING.md](CONTRIBUTING.md) for guidelines.

### Development Setup
```bash
# Install development dependencies
pip install -r requirements-dev.txt

# Install pre-commit hooks
pre-commit install

# Run code formatting
black src/ tests/
isort src/ tests/

# Run linting
flake8 src/ tests/
mypy src/
```

## 📚 Documentation

- 📖 [API Documentation](docs/api.md)
- 🏥 [Clinical Guidelines](docs/clinical_guidelines.md)
- 🔧 [Configuration Guide](docs/configuration.md)
- 🚀 [Deployment Guide](docs/deployment.md)
- 🧪 [Testing Guide](docs/testing.md)

## 🆘 Support

- 📧 Email: <EMAIL>
- 💬 Discord: [Join our community](https://discord.gg/healthcare-ai)
- 🐛 Issues: [GitHub Issues](https://github.com/healthcare-ai/triage-agent/issues)
- 📖 Wiki: [Project Wiki](https://github.com/healthcare-ai/triage-agent/wiki)

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## ⚠️ Important Disclaimers

- 🏥 **For Research/Educational Use**: This system is for research and educational purposes
- 👨‍⚕️ **Not a Medical Device**: Not approved for clinical use without proper validation
- 🔍 **Human Oversight Required**: All AI recommendations require human review
- 📋 **Regulatory Approval Needed**: Clinical deployment requires regulatory approval
- 🔒 **Data Privacy**: Ensure compliance with local healthcare data regulations

## 🙏 Acknowledgments

- 🧠 **BraTS Challenge**: Brain tumor segmentation datasets
- 🔬 **MONAI**: Medical imaging framework
- 🤖 **LangChain**: LLM orchestration framework
- 🎯 **PaliGemma 2**: Multimodal AI capabilities
- 🏥 **Medical Community**: Clinical expertise and validation
- 🌟 **Open Source Community**: Tools and libraries that make this possible

---

**Built with ❤️ for advancing healthcare AI while prioritizing patient safety and clinical excellence.**
