#!/usr/bin/env python3
"""
Inference script for Healthcare Multimodal Triage Agent.

This script demonstrates the complete triage pipeline:
1. Load and preprocess MRI data
2. Perform brain tumor segmentation using PaliGemma 2
3. Process patient history data
4. Run triage agent for priority assessment
5. Generate comprehensive triage report
"""

import os
import sys
import logging
import argparse
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, Optional

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from healthcare_triage.imaging import MRIProcessor, PaliGemmaSegmenter
from healthcare_triage.nlp import PatientHistoryProcessor
from healthcare_triage.agents import TriageAgent
from healthcare_triage.utils.config import load_config
from healthcare_triage.utils.logging_utils import setup_comprehensive_logging, performance_logger


class TriageInferencePipeline:
    """Complete inference pipeline for healthcare triage."""
    
    def __init__(self, config_path: Optional[str] = None, model_path: Optional[str] = None):
        """
        Initialize the inference pipeline.
        
        Args:
            config_path: Path to configuration file
            model_path: Path to trained model (optional)
        """
        self.config = load_config(config_path)
        self.logger = setup_comprehensive_logging(self.config)
        
        # Initialize components
        self.mri_processor = MRIProcessor(self.config)
        self.segmenter = PaliGemmaSegmenter(self.config)
        self.patient_processor = PatientHistoryProcessor(self.config)
        self.triage_agent = TriageAgent(self.config)
        
        # Load trained model if specified
        if model_path and Path(model_path).exists():
            self.segmenter.load_model(model_path)
            self.logger.info(f"Loaded trained model from {model_path}")
        
        self.logger.info("Triage inference pipeline initialized")
    
    def run_complete_triage(
        self,
        mri_image_path: str,
        patient_id: str,
        patient_data_sources: Optional[list] = None
    ) -> Dict[str, Any]:
        """
        Run complete triage assessment for a patient.
        
        Args:
            mri_image_path: Path to MRI image file
            patient_id: Patient identifier
            patient_data_sources: List of data sources to load
            
        Returns:
            Complete triage assessment
        """
        self.logger.info(f"Starting complete triage for patient {patient_id}")
        performance_logger.start_timer("complete_triage")
        
        try:
            # Step 1: Process MRI image
            self.logger.info("Step 1: Processing MRI image...")
            performance_logger.start_timer("mri_processing")
            
            mri_results = self.process_mri_image(mri_image_path)
            
            performance_logger.end_timer("mri_processing")
            
            # Step 2: Load and process patient history
            self.logger.info("Step 2: Processing patient history...")
            performance_logger.start_timer("patient_history")
            
            patient_data = self.process_patient_history(patient_id, patient_data_sources)
            
            performance_logger.end_timer("patient_history")
            
            # Step 3: Run triage assessment
            self.logger.info("Step 3: Running triage assessment...")
            performance_logger.start_timer("triage_assessment")
            
            triage_results = self.triage_agent.perform_triage(patient_data, mri_results)
            
            performance_logger.end_timer("triage_assessment")
            
            # Step 4: Generate comprehensive report
            self.logger.info("Step 4: Generating comprehensive report...")
            
            complete_assessment = self.generate_comprehensive_report(
                patient_id, mri_results, patient_data, triage_results
            )
            
            performance_logger.end_timer("complete_triage")
            self.logger.info(f"Complete triage assessment finished for patient {patient_id}")
            
            return complete_assessment
            
        except Exception as e:
            self.logger.error(f"Triage assessment failed for patient {patient_id}: {e}")
            raise
    
    def process_mri_image(self, image_path: str) -> Dict[str, Any]:
        """
        Process MRI image and perform segmentation.
        
        Args:
            image_path: Path to MRI image
            
        Returns:
            MRI processing results
        """
        if not Path(image_path).exists():
            raise FileNotFoundError(f"MRI image not found: {image_path}")
        
        # Preprocess image
        processed_image = self.mri_processor.preprocess_single_image(image_path)
        
        # Perform segmentation
        segmentation_result = self.segmenter.segment_image(
            processed_image,
            prompt="Analyze this brain MRI image for tumor detection and segmentation"
        )
        
        # Add metadata
        segmentation_result.update({
            "original_image_path": image_path,
            "processed_image_shape": list(processed_image.shape),
            "processing_timestamp": datetime.now().isoformat()
        })
        
        return segmentation_result
    
    def process_patient_history(
        self,
        patient_id: str,
        data_sources: Optional[list] = None
    ) -> Dict[str, Any]:
        """
        Load and process patient history data.
        
        Args:
            patient_id: Patient identifier
            data_sources: List of data sources to load
            
        Returns:
            Processed patient data
        """
        # Load patient data
        raw_patient_data = self.patient_processor.load_patient_data(
            patient_id, data_sources
        )
        
        # Process the data
        processed_data = self.patient_processor.process_patient_history(raw_patient_data)
        
        return processed_data
    
    def generate_comprehensive_report(
        self,
        patient_id: str,
        mri_results: Dict[str, Any],
        patient_data: Dict[str, Any],
        triage_results: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Generate comprehensive triage report.
        
        Args:
            patient_id: Patient identifier
            mri_results: MRI analysis results
            patient_data: Patient history data
            triage_results: Triage assessment results
            
        Returns:
            Comprehensive assessment report
        """
        report = {
            "patient_id": patient_id,
            "assessment_timestamp": datetime.now().isoformat(),
            "report_version": "1.0",
            
            # Executive Summary
            "executive_summary": {
                "priority_level": triage_results.get("priority_level", "Unknown"),
                "confidence_score": triage_results.get("confidence_score", 0.0),
                "key_findings": self._extract_key_findings(mri_results, patient_data),
                "immediate_actions": self._extract_immediate_actions(triage_results)
            },
            
            # Imaging Analysis
            "imaging_analysis": {
                "modality": "MRI Brain",
                "ai_interpretation": mri_results.get("generated_text", "No interpretation available"),
                "segmentation_findings": mri_results.get("segmentation_info", {}),
                "technical_quality": "Adequate for interpretation"  # Could be enhanced
            },
            
            # Patient History Summary
            "patient_history": {
                "summary": patient_data.get("summary", {}),
                "risk_factors": patient_data.get("processed_data", {}).get("risk_factors", []),
                "relevant_history": self._extract_relevant_history(patient_data)
            },
            
            # Triage Assessment
            "triage_assessment": triage_results,
            
            # Recommendations
            "recommendations": {
                "immediate_actions": triage_results.get("recommendations", []),
                "follow_up_timeline": triage_results.get("reassessment_timeline", ""),
                "monitoring_parameters": triage_results.get("red_flags", []),
                "specialist_referrals": self._suggest_referrals(mri_results, triage_results)
            },
            
            # Quality Metrics
            "quality_metrics": {
                "ai_confidence": triage_results.get("confidence_score", 0.0),
                "data_completeness": self._assess_data_completeness(patient_data),
                "imaging_quality": "Good",  # Could be automated
                "recommendation_strength": self._assess_recommendation_strength(triage_results)
            },
            
            # Compliance and Audit
            "compliance": {
                "hipaa_compliant": True,
                "audit_trail": f"Automated triage assessment performed at {datetime.now().isoformat()}",
                "human_review_required": triage_results.get("priority_level") in ["Emergency", "Urgent"]
            }
        }
        
        return report
    
    def _extract_key_findings(self, mri_results: Dict, patient_data: Dict) -> list:
        """Extract key findings from analysis."""
        findings = []
        
        # MRI findings
        seg_info = mri_results.get("segmentation_info", {})
        if seg_info.get("has_tumor"):
            tumor_desc = f"Brain tumor detected"
            if seg_info.get("tumor_type"):
                tumor_desc += f" ({seg_info['tumor_type']})"
            if seg_info.get("location"):
                tumor_desc += f" in {seg_info['location']} region"
            findings.append(tumor_desc)
        
        # Patient history findings
        summary = patient_data.get("summary", {})
        if summary.get("primary_diagnoses"):
            findings.append(f"Known diagnoses: {', '.join(map(str, summary['primary_diagnoses'][:2]))}")
        
        return findings
    
    def _extract_immediate_actions(self, triage_results: Dict) -> list:
        """Extract immediate actions from triage results."""
        actions = []
        
        priority = triage_results.get("priority_level", "")
        if priority == "Emergency":
            actions.extend([
                "Immediate physician evaluation required",
                "Continuous monitoring",
                "Prepare for potential emergency intervention"
            ])
        elif priority == "Urgent":
            actions.extend([
                "Physician evaluation within 1-2 hours",
                "Regular vital sign monitoring"
            ])
        
        # Add specific recommendations
        recommendations = triage_results.get("recommendations", [])
        actions.extend(recommendations[:3])  # Top 3 recommendations
        
        return actions
    
    def _extract_relevant_history(self, patient_data: Dict) -> Dict:
        """Extract relevant medical history."""
        summary = patient_data.get("summary", {})
        
        return {
            "current_medications": summary.get("current_medications", [])[:5],
            "known_allergies": summary.get("known_allergies", []),
            "recent_imaging": summary.get("recent_imaging", [])[:3]
        }
    
    def _suggest_referrals(self, mri_results: Dict, triage_results: Dict) -> list:
        """Suggest specialist referrals based on findings."""
        referrals = []
        
        # Based on imaging findings
        seg_info = mri_results.get("segmentation_info", {})
        if seg_info.get("has_tumor"):
            referrals.extend(["Neurosurgery", "Neuro-oncology"])
        
        # Based on priority level
        priority = triage_results.get("priority_level", "")
        if priority in ["Emergency", "Urgent"]:
            referrals.append("Emergency Medicine")
        
        return list(set(referrals))  # Remove duplicates
    
    def _assess_data_completeness(self, patient_data: Dict) -> float:
        """Assess completeness of patient data."""
        total_sources = 4  # ehr, documents, lab_reports, imaging_reports
        available_sources = len([
            source for source in patient_data.get("raw_data", {}).values()
            if source is not None
        ])
        
        return available_sources / total_sources
    
    def _assess_recommendation_strength(self, triage_results: Dict) -> str:
        """Assess strength of recommendations."""
        confidence = triage_results.get("confidence_score", 0.0)
        
        if confidence >= 0.8:
            return "High"
        elif confidence >= 0.6:
            return "Moderate"
        else:
            return "Low"
    
    def save_report(self, report: Dict[str, Any], output_path: str):
        """Save triage report to file."""
        import json
        
        output_path = Path(output_path)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        self.logger.info(f"Triage report saved to {output_path}")
    
    def visualize_results(self, report: Dict[str, Any], output_dir: str):
        """Create visualizations of the triage results."""
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # This would create various visualizations
        # For now, just log the action
        self.logger.info(f"Visualizations would be saved to {output_dir}")


def main():
    """Main inference function."""
    parser = argparse.ArgumentParser(description="Run Healthcare Triage Agent Inference")
    parser.add_argument("--mri-image", type=str, required=True, help="Path to MRI image file")
    parser.add_argument("--patient-id", type=str, required=True, help="Patient identifier")
    parser.add_argument("--config", type=str, help="Path to configuration file")
    parser.add_argument("--model-path", type=str, help="Path to trained model")
    parser.add_argument("--output-dir", type=str, default="outputs/inference", help="Output directory")
    parser.add_argument("--data-sources", nargs="+", help="Patient data sources to load")
    parser.add_argument("--save-report", action="store_true", help="Save detailed report")
    parser.add_argument("--visualize", action="store_true", help="Create visualizations")
    
    args = parser.parse_args()
    
    try:
        # Initialize pipeline
        pipeline = TriageInferencePipeline(args.config, args.model_path)
        
        # Run complete triage assessment
        report = pipeline.run_complete_triage(
            mri_image_path=args.mri_image,
            patient_id=args.patient_id,
            patient_data_sources=args.data_sources
        )
        
        # Display results
        print("\n" + "="*80)
        print("HEALTHCARE MULTIMODAL TRIAGE ASSESSMENT")
        print("="*80)
        
        exec_summary = report["executive_summary"]
        print(f"\nPatient ID: {report['patient_id']}")
        print(f"Assessment Time: {report['assessment_timestamp']}")
        print(f"\nPRIORITY LEVEL: {exec_summary['priority_level']}")
        print(f"Confidence Score: {exec_summary['confidence_score']:.2f}")
        
        print(f"\nKey Findings:")
        for finding in exec_summary['key_findings']:
            print(f"  • {finding}")
        
        print(f"\nImmediate Actions:")
        for action in exec_summary['immediate_actions']:
            print(f"  • {action}")
        
        # Triage details
        triage = report["triage_assessment"]
        print(f"\nTriage Reasoning:")
        print(f"  {triage.get('reasoning', 'No reasoning provided')}")
        
        print(f"\nReassessment Timeline: {triage.get('reassessment_timeline', 'Not specified')}")
        
        if triage.get('red_flags'):
            print(f"\nRed Flags to Monitor:")
            for flag in triage['red_flags']:
                print(f"  ⚠️  {flag}")
        
        # Save detailed report if requested
        if args.save_report:
            output_path = Path(args.output_dir) / f"{args.patient_id}_triage_report.json"
            pipeline.save_report(report, str(output_path))
            print(f"\nDetailed report saved to: {output_path}")
        
        # Create visualizations if requested
        if args.visualize:
            viz_dir = Path(args.output_dir) / "visualizations" / args.patient_id
            pipeline.visualize_results(report, str(viz_dir))
            print(f"Visualizations saved to: {viz_dir}")
        
        print("\n" + "="*80)
        print("ASSESSMENT COMPLETED")
        print("="*80)
        
    except FileNotFoundError as e:
        print(f"Error: {e}")
        print("Please ensure the MRI image file exists and patient data is available.")
        sys.exit(1)
    except Exception as e:
        print(f"Inference failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
