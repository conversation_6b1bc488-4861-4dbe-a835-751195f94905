#!/usr/bin/env python3
"""
Simplified training script for Healthcare Multimodal Triage Agent.

This script demonstrates the training pipeline without PaliGemma dependencies
to avoid DLL conflicts. It focuses on the MRI processing and data pipeline.
"""

import os
import sys
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, Optional

import torch
import numpy as np
import nibabel as nib
from torch.utils.data import Dataset, DataLoader

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from healthcare_triage.imaging import MRIProcessor
from healthcare_triage.nlp import PatientHistoryProcessor
from healthcare_triage.utils.config import load_config
from healthcare_triage.utils.logging_utils import setup_comprehensive_logging


class SimpleBrainMRIDataset(Dataset):
    """Simple dataset for brain MRI data."""
    
    def __init__(self, data_dir: Path, split: str = "train"):
        """Initialize dataset."""
        self.data_dir = data_dir / split
        self.image_dir = self.data_dir / "images"
        self.label_dir = self.data_dir / "labels"
        
        # Find all image files
        self.image_files = list(self.image_dir.glob("*.nii.gz"))
        self.label_files = []
        
        # Find corresponding label files
        for img_file in self.image_files:
            label_file = self.label_dir / img_file.name.replace("_image", "_label")
            if label_file.exists():
                self.label_files.append(label_file)
            else:
                self.label_files.append(None)
        
        print(f"Found {len(self.image_files)} images for {split} split")
    
    def __len__(self):
        return len(self.image_files)
    
    def __getitem__(self, idx):
        """Get item from dataset."""
        # Load image
        img_path = self.image_files[idx]
        img_nii = nib.load(str(img_path))
        image = img_nii.get_fdata()
        
        # Load label if available
        label_path = self.label_files[idx]
        if label_path and label_path.exists():
            label_nii = nib.load(str(label_path))
            label = label_nii.get_fdata()
        else:
            label = np.zeros_like(image)
        
        # Convert to tensors
        image = torch.from_numpy(image).float()
        label = torch.from_numpy(label).float()
        
        # Add channel dimension if needed
        if image.ndim == 3:
            image = image.unsqueeze(0)
        if label.ndim == 3:
            label = label.unsqueeze(0)
        
        return {
            "image": image,
            "label": label,
            "image_path": str(img_path),
            "patient_id": img_path.stem.replace("_image", "")
        }


class SimpleSegmentationModel(torch.nn.Module):
    """Simple 3D U-Net-like model for demonstration."""
    
    def __init__(self, in_channels=1, out_channels=1):
        """Initialize model."""
        super().__init__()
        
        # Simple encoder-decoder architecture
        self.encoder = torch.nn.Sequential(
            torch.nn.Conv3d(in_channels, 32, 3, padding=1),
            torch.nn.ReLU(),
            torch.nn.Conv3d(32, 64, 3, padding=1),
            torch.nn.ReLU(),
            torch.nn.AdaptiveAvgPool3d((32, 32, 32))
        )
        
        self.decoder = torch.nn.Sequential(
            torch.nn.Conv3d(64, 32, 3, padding=1),
            torch.nn.ReLU(),
            torch.nn.Conv3d(32, out_channels, 3, padding=1),
            torch.nn.Sigmoid()
        )
    
    def forward(self, x):
        """Forward pass."""
        # Encode
        encoded = self.encoder(x)
        
        # Decode
        decoded = self.decoder(encoded)
        
        # Resize to match input
        decoded = torch.nn.functional.interpolate(
            decoded, size=x.shape[2:], mode='trilinear', align_corners=False
        )
        
        return decoded


class SimpleTrainer:
    """Simple trainer for demonstration."""
    
    def __init__(self, config_path: Optional[str] = None):
        """Initialize trainer."""
        self.config = load_config(config_path)
        self.logger = setup_comprehensive_logging(self.config)
        
        # Set device
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.logger.info(f"Using device: {self.device}")
        
        # Initialize components
        self.mri_processor = MRIProcessor(self.config)
        self.patient_processor = PatientHistoryProcessor(self.config)
        
        # Initialize model
        self.model = SimpleSegmentationModel().to(self.device)
        self.optimizer = torch.optim.Adam(self.model.parameters(), lr=1e-4)
        self.criterion = torch.nn.BCELoss()
        
        self.logger.info("Simple trainer initialized")
    
    def prepare_datasets(self):
        """Prepare datasets."""
        self.logger.info("Preparing datasets...")
        
        data_dir = Path(self.config.data.mri_data_path)
        
        # Create datasets
        train_dataset = SimpleBrainMRIDataset(data_dir, "train")
        val_dataset = SimpleBrainMRIDataset(data_dir, "val")
        
        # Create data loaders
        self.train_loader = DataLoader(
            train_dataset, 
            batch_size=1,  # Small batch size for demo
            shuffle=True,
            num_workers=0  # Avoid multiprocessing issues
        )
        
        self.val_loader = DataLoader(
            val_dataset,
            batch_size=1,
            shuffle=False,
            num_workers=0
        )
        
        self.logger.info(f"Training samples: {len(train_dataset)}")
        self.logger.info(f"Validation samples: {len(val_dataset)}")
        
        return train_dataset, val_dataset
    
    def train_epoch(self, epoch: int):
        """Train for one epoch."""
        self.model.train()
        total_loss = 0.0
        num_batches = 0
        
        for batch_idx, batch in enumerate(self.train_loader):
            # Move to device
            images = batch["image"].to(self.device)
            labels = batch["label"].to(self.device)
            
            # Forward pass
            self.optimizer.zero_grad()
            outputs = self.model(images)
            
            # Calculate loss
            loss = self.criterion(outputs, labels)
            
            # Backward pass
            loss.backward()
            self.optimizer.step()
            
            total_loss += loss.item()
            num_batches += 1
            
            if batch_idx % 5 == 0:
                self.logger.info(
                    f"Epoch {epoch}, Batch {batch_idx}/{len(self.train_loader)}, "
                    f"Loss: {loss.item():.4f}"
                )
        
        avg_loss = total_loss / num_batches if num_batches > 0 else 0.0
        self.logger.info(f"Epoch {epoch} - Average training loss: {avg_loss:.4f}")
        
        return avg_loss
    
    def validate(self, epoch: int):
        """Validate the model."""
        self.model.eval()
        total_loss = 0.0
        num_batches = 0
        
        with torch.no_grad():
            for batch in self.val_loader:
                images = batch["image"].to(self.device)
                labels = batch["label"].to(self.device)
                
                outputs = self.model(images)
                loss = self.criterion(outputs, labels)
                
                total_loss += loss.item()
                num_batches += 1
        
        avg_loss = total_loss / num_batches if num_batches > 0 else 0.0
        self.logger.info(f"Epoch {epoch} - Average validation loss: {avg_loss:.4f}")
        
        return avg_loss
    
    def train(self, num_epochs: int = 5):
        """Train the model."""
        self.logger.info(f"Starting training for {num_epochs} epochs...")
        
        # Prepare datasets
        train_dataset, val_dataset = self.prepare_datasets()
        
        if len(train_dataset) == 0:
            self.logger.warning("No training data found!")
            return
        
        # Training loop
        for epoch in range(num_epochs):
            self.logger.info(f"Starting epoch {epoch + 1}/{num_epochs}")
            
            # Train
            train_loss = self.train_epoch(epoch + 1)
            
            # Validate
            val_loss = self.validate(epoch + 1)
            
            # Save checkpoint
            if (epoch + 1) % 2 == 0:
                checkpoint_path = f"models/checkpoints/simple_model_epoch_{epoch + 1}.pth"
                os.makedirs(os.path.dirname(checkpoint_path), exist_ok=True)
                torch.save({
                    'epoch': epoch + 1,
                    'model_state_dict': self.model.state_dict(),
                    'optimizer_state_dict': self.optimizer.state_dict(),
                    'train_loss': train_loss,
                    'val_loss': val_loss,
                }, checkpoint_path)
                self.logger.info(f"Checkpoint saved: {checkpoint_path}")
        
        self.logger.info("Training completed!")
    
    def test_patient_processing(self):
        """Test patient history processing."""
        self.logger.info("Testing patient history processing...")
        
        try:
            # Load sample patient data
            patient_data = self.patient_processor.load_patient_data("patient_001")
            processed_data = self.patient_processor.process_patient_history(patient_data)
            
            self.logger.info("Patient processing test completed")
            return processed_data
            
        except Exception as e:
            self.logger.warning(f"Patient processing test failed: {e}")
            return None
    
    def run_complete_demo(self):
        """Run complete demonstration."""
        self.logger.info("Running complete demonstration...")
        
        # Test patient processing
        patient_data = self.test_patient_processing()
        
        # Run training
        self.train(num_epochs=3)
        
        # Test inference on a sample
        self.test_inference()
        
        self.logger.info("Complete demonstration finished!")
    
    def test_inference(self):
        """Test inference on a sample."""
        self.logger.info("Testing inference...")
        
        try:
            # Get a sample from validation set
            if hasattr(self, 'val_loader') and len(self.val_loader) > 0:
                sample = next(iter(self.val_loader))
                
                self.model.eval()
                with torch.no_grad():
                    image = sample["image"].to(self.device)
                    output = self.model(image)
                    
                    self.logger.info(f"Inference completed - Output shape: {output.shape}")
                    
                    # Save sample result
                    output_dir = Path("outputs/inference")
                    output_dir.mkdir(parents=True, exist_ok=True)
                    
                    # Convert to numpy and save
                    output_np = output.cpu().numpy()[0, 0]  # Remove batch and channel dims
                    
                    # Create NIfTI image
                    nii_img = nib.Nifti1Image(output_np, np.eye(4))
                    nib.save(nii_img, str(output_dir / "sample_prediction.nii.gz"))
                    
                    self.logger.info(f"Sample prediction saved to {output_dir}")
            else:
                self.logger.warning("No validation data available for inference test")
                
        except Exception as e:
            self.logger.warning(f"Inference test failed: {e}")


def main():
    """Main function."""
    try:
        print("🚀 HEALTHCARE TRIAGE AGENT - SIMPLIFIED TRAINING")
        print("=" * 60)
        
        trainer = SimpleTrainer()
        trainer.run_complete_demo()
        
        print("\n🎉 Training demonstration completed!")
        print("\nNext steps:")
        print("1. Check outputs/inference/ for sample predictions")
        print("2. Review models/checkpoints/ for saved models")
        print("3. Run full inference: python scripts/run_inference.py")
        
    except Exception as e:
        print(f"Training failed: {e}")
        logging.error(f"Training failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
