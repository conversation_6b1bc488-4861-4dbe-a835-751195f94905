# Healthcare Multimodal Triage Agent Configuration

# Project settings
project:
  name: "healthcare-triage-agent"
  version: "0.1.0"
  description: "Healthcare Multimodal Triage Agent using PaliGemma 2 and <PERSON><PERSON><PERSON><PERSON>"

# Data paths
data:
  mri_data_path: "data/mri"
  patient_records_path: "data/patient_records"
  knowledge_base_path: "data/knowledge_base"
  processed_data_path: "data/processed"

# Model configurations
models:
  paligemma:
    model_name: "google/paligemma2-3b-pt-224"
    checkpoint_path: "models/paligemma/checkpoints"
    max_length: 512
    image_size: 224
    batch_size: 4
    learning_rate: 1e-5
    num_epochs: 10
    
  llm:
    model_name: "gpt-4"
    temperature: 0.1
    max_tokens: 2048
    
# MONAI preprocessing settings
preprocessing:
  image_size: [224, 224, 224]
  spacing: [1.0, 1.0, 1.0]
  intensity_range: [0, 1]
  augmentation:
    rotation_range: 15
    zoom_range: 0.1
    flip_probability: 0.5

# LangChain agent settings
agents:
  triage_agent:
    model: "gpt-4"
    temperature: 0.1
    max_iterations: 5
    
  knowledge_agent:
    model: "gpt-4"
    temperature: 0.0
    retrieval_k: 5

# Vector database settings
vector_db:
  provider: "qdrant"
  host: "localhost"
  port: 6333
  collection_name: "medical_knowledge"
  embedding_model: "sentence-transformers/all-MiniLM-L6-v2"

# API settings
api:
  host: "0.0.0.0"
  port: 8000
  debug: false
  cors_origins: ["*"]

# Logging settings
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "logs/healthcare_triage.log"

# Security and compliance
security:
  enable_encryption: true
  hipaa_compliance: true
  audit_logging: true
  
# Training settings
training:
  device: "cuda"
  mixed_precision: true
  gradient_accumulation_steps: 4
  warmup_steps: 1000
  save_steps: 500
  eval_steps: 100
  
# Evaluation metrics
evaluation:
  segmentation_metrics:
    - "dice_score"
    - "iou"
    - "sensitivity"
    - "specificity"
  nlp_metrics:
    - "bleu"
    - "rouge"
    - "bertscore"
