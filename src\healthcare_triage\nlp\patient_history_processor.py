"""
Patient History Processor for analyzing medical records and patient data.
"""

import logging
import re
from typing import Dict, List, Optional, Tuple, Union, Any
from pathlib import Path
from datetime import datetime

import pandas as pd
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.schema import Document

from ..utils.config import Config
from .document_parser import DocumentParser
from .text_summarizer import TextSummarizer
from .information_extractor import InformationExtractor

logger = logging.getLogger(__name__)


class PatientHistoryProcessor:
    """
    Main class for processing patient history and medical records.
    
    This class handles:
    - Loading patient data from various sources
    - Parsing medical documents
    - Extracting key medical information
    - Summarizing patient history
    - Preparing data for LLM agents
    """
    
    def __init__(self, config: Config):
        """
        Initialize patient history processor.
        
        Args:
            config: Configuration object
        """
        self.config = config
        self.document_parser = DocumentParser(config)
        self.text_summarizer = TextSummarizer(config)
        self.information_extractor = InformationExtractor(config)
        
        # Text splitter for chunking long documents
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=1000,
            chunk_overlap=200,
            length_function=len,
            separators=["\n\n", "\n", ". ", " ", ""]
        )
        
    def load_patient_data(
        self, 
        patient_id: str,
        data_sources: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        Load comprehensive patient data from multiple sources.
        
        Args:
            patient_id: Unique patient identifier
            data_sources: List of data sources to load from
            
        Returns:
            Dictionary containing all patient data
        """
        if data_sources is None:
            data_sources = ["ehr", "documents", "lab_reports", "imaging_reports"]
        
        patient_data = {
            "patient_id": patient_id,
            "loaded_at": datetime.now().isoformat(),
            "data_sources": data_sources,
            "raw_data": {},
            "processed_data": {},
            "summary": {},
            "key_findings": []
        }
        
        for source in data_sources:
            try:
                source_data = self._load_from_source(patient_id, source)
                patient_data["raw_data"][source] = source_data
                logger.info(f"Loaded {source} data for patient {patient_id}")
            except Exception as e:
                logger.warning(f"Failed to load {source} data for patient {patient_id}: {e}")
                patient_data["raw_data"][source] = None
        
        return patient_data
    
    def _load_from_source(self, patient_id: str, source: str) -> Dict[str, Any]:
        """
        Load data from specific source.
        
        Args:
            patient_id: Patient identifier
            source: Data source name
            
        Returns:
            Source-specific data
        """
        source_path = Path(self.config.data.patient_records_path) / source
        
        if source == "ehr":
            return self._load_ehr_data(patient_id, source_path)
        elif source == "documents":
            return self._load_documents(patient_id, source_path)
        elif source == "lab_reports":
            return self._load_lab_reports(patient_id, source_path)
        elif source == "imaging_reports":
            return self._load_imaging_reports(patient_id, source_path)
        else:
            raise ValueError(f"Unknown data source: {source}")
    
    def _load_ehr_data(self, patient_id: str, source_path: Path) -> Dict[str, Any]:
        """Load structured EHR data."""
        ehr_file = source_path / f"{patient_id}_ehr.json"
        
        if ehr_file.exists():
            import json
            with open(ehr_file, 'r') as f:
                return json.load(f)
        else:
            # Return empty structure
            return {
                "demographics": {},
                "medical_history": [],
                "medications": [],
                "allergies": [],
                "vital_signs": [],
                "diagnoses": []
            }
    
    def _load_documents(self, patient_id: str, source_path: Path) -> List[Dict[str, Any]]:
        """Load and parse medical documents."""
        documents = []
        doc_dir = source_path / patient_id
        
        if doc_dir.exists():
            for doc_file in doc_dir.glob("*"):
                if doc_file.suffix.lower() in ['.pdf', '.txt', '.docx']:
                    try:
                        parsed_doc = self.document_parser.parse_document(str(doc_file))
                        documents.append({
                            "filename": doc_file.name,
                            "type": self._infer_document_type(doc_file.name),
                            "content": parsed_doc["content"],
                            "metadata": parsed_doc["metadata"]
                        })
                    except Exception as e:
                        logger.warning(f"Failed to parse document {doc_file}: {e}")
        
        return documents
    
    def _load_lab_reports(self, patient_id: str, source_path: Path) -> List[Dict[str, Any]]:
        """Load laboratory reports."""
        lab_reports = []
        lab_file = source_path / f"{patient_id}_labs.csv"
        
        if lab_file.exists():
            try:
                df = pd.read_csv(lab_file)
                lab_reports = df.to_dict('records')
            except Exception as e:
                logger.warning(f"Failed to load lab reports: {e}")
        
        return lab_reports
    
    def _load_imaging_reports(self, patient_id: str, source_path: Path) -> List[Dict[str, Any]]:
        """Load imaging reports."""
        imaging_reports = []
        imaging_dir = source_path / patient_id
        
        if imaging_dir.exists():
            for report_file in imaging_dir.glob("*.txt"):
                try:
                    with open(report_file, 'r') as f:
                        content = f.read()
                    
                    imaging_reports.append({
                        "filename": report_file.name,
                        "modality": self._extract_modality(report_file.name),
                        "date": self._extract_date(content),
                        "content": content,
                        "findings": self._extract_findings(content)
                    })
                except Exception as e:
                    logger.warning(f"Failed to load imaging report {report_file}: {e}")
        
        return imaging_reports
    
    def _infer_document_type(self, filename: str) -> str:
        """Infer document type from filename."""
        filename_lower = filename.lower()
        
        if any(term in filename_lower for term in ['discharge', 'summary']):
            return "discharge_summary"
        elif any(term in filename_lower for term in ['progress', 'note']):
            return "progress_note"
        elif any(term in filename_lower for term in ['consult', 'consultation']):
            return "consultation"
        elif any(term in filename_lower for term in ['lab', 'laboratory']):
            return "lab_report"
        elif any(term in filename_lower for term in ['radiology', 'imaging', 'mri', 'ct', 'xray']):
            return "imaging_report"
        else:
            return "unknown"
    
    def _extract_modality(self, filename: str) -> str:
        """Extract imaging modality from filename."""
        filename_lower = filename.lower()
        
        if 'mri' in filename_lower:
            return "MRI"
        elif 'ct' in filename_lower:
            return "CT"
        elif 'xray' in filename_lower or 'x-ray' in filename_lower:
            return "X-Ray"
        elif 'ultrasound' in filename_lower or 'us' in filename_lower:
            return "Ultrasound"
        else:
            return "Unknown"
    
    def _extract_date(self, content: str) -> Optional[str]:
        """Extract date from document content."""
        # Simple date extraction - can be improved
        date_patterns = [
            r'\d{1,2}/\d{1,2}/\d{4}',
            r'\d{4}-\d{2}-\d{2}',
            r'\d{1,2}-\d{1,2}-\d{4}'
        ]
        
        for pattern in date_patterns:
            match = re.search(pattern, content)
            if match:
                return match.group()
        
        return None
    
    def _extract_findings(self, content: str) -> List[str]:
        """Extract key findings from imaging report."""
        findings = []
        
        # Look for findings section
        findings_match = re.search(r'FINDINGS?:(.+?)(?:IMPRESSION|CONCLUSION|$)', content, re.IGNORECASE | re.DOTALL)
        if findings_match:
            findings_text = findings_match.group(1).strip()
            # Split by sentences or bullet points
            sentences = re.split(r'[.!?]\s+|\n\s*[-•]\s*', findings_text)
            findings = [s.strip() for s in sentences if s.strip()]
        
        return findings
    
    def process_patient_history(self, patient_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process and analyze patient history data.
        
        Args:
            patient_data: Raw patient data dictionary
            
        Returns:
            Processed patient data with analysis
        """
        processed_data = patient_data.copy()
        
        # Extract key information from all sources
        processed_data["processed_data"]["key_information"] = self._extract_key_information(patient_data)
        
        # Generate summaries
        processed_data["processed_data"]["summaries"] = self._generate_summaries(patient_data)
        
        # Extract timeline
        processed_data["processed_data"]["timeline"] = self._create_timeline(patient_data)
        
        # Identify risk factors
        processed_data["processed_data"]["risk_factors"] = self._identify_risk_factors(patient_data)
        
        # Generate overall summary
        processed_data["summary"] = self._generate_overall_summary(processed_data)
        
        logger.info(f"Processed patient history for {patient_data['patient_id']}")
        return processed_data
    
    def _extract_key_information(self, patient_data: Dict[str, Any]) -> Dict[str, Any]:
        """Extract key medical information from all sources."""
        key_info = {
            "demographics": {},
            "chief_complaints": [],
            "diagnoses": [],
            "medications": [],
            "allergies": [],
            "procedures": [],
            "lab_values": [],
            "imaging_findings": []
        }
        
        # Extract from EHR data
        if patient_data["raw_data"].get("ehr"):
            ehr_data = patient_data["raw_data"]["ehr"]
            key_info["demographics"] = ehr_data.get("demographics", {})
            key_info["diagnoses"].extend(ehr_data.get("diagnoses", []))
            key_info["medications"].extend(ehr_data.get("medications", []))
            key_info["allergies"].extend(ehr_data.get("allergies", []))
        
        # Extract from documents
        if patient_data["raw_data"].get("documents"):
            for doc in patient_data["raw_data"]["documents"]:
                extracted = self.information_extractor.extract_from_text(doc["content"])
                for key in key_info:
                    if key in extracted:
                        key_info[key].extend(extracted[key])
        
        # Extract from lab reports
        if patient_data["raw_data"].get("lab_reports"):
            key_info["lab_values"] = patient_data["raw_data"]["lab_reports"]
        
        # Extract from imaging reports
        if patient_data["raw_data"].get("imaging_reports"):
            for report in patient_data["raw_data"]["imaging_reports"]:
                key_info["imaging_findings"].extend(report.get("findings", []))
        
        return key_info
    
    def _generate_summaries(self, patient_data: Dict[str, Any]) -> Dict[str, str]:
        """Generate summaries for different data types."""
        summaries = {}
        
        # Summarize documents
        if patient_data["raw_data"].get("documents"):
            doc_texts = []
            for doc in patient_data["raw_data"]["documents"]:
                doc_texts.append(doc["content"])
            
            if doc_texts:
                combined_text = "\n\n".join(doc_texts)
                summaries["documents"] = self.text_summarizer.summarize_text(combined_text)
        
        # Summarize imaging reports
        if patient_data["raw_data"].get("imaging_reports"):
            imaging_texts = []
            for report in patient_data["raw_data"]["imaging_reports"]:
                imaging_texts.append(report["content"])
            
            if imaging_texts:
                combined_imaging = "\n\n".join(imaging_texts)
                summaries["imaging"] = self.text_summarizer.summarize_text(combined_imaging)
        
        return summaries
    
    def _create_timeline(self, patient_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Create chronological timeline of patient events."""
        timeline_events = []
        
        # Add events from various sources with dates
        # This is a simplified implementation
        
        if patient_data["raw_data"].get("imaging_reports"):
            for report in patient_data["raw_data"]["imaging_reports"]:
                if report.get("date"):
                    timeline_events.append({
                        "date": report["date"],
                        "type": "imaging",
                        "description": f"{report['modality']} - {report['filename']}",
                        "details": report.get("findings", [])
                    })
        
        # Sort by date
        timeline_events.sort(key=lambda x: x["date"] if x["date"] else "")
        
        return timeline_events
    
    def _identify_risk_factors(self, patient_data: Dict[str, Any]) -> List[str]:
        """Identify potential risk factors from patient data."""
        risk_factors = []
        
        # This is a simplified implementation
        # In practice, you'd use more sophisticated medical knowledge
        
        key_info = patient_data.get("processed_data", {}).get("key_information", {})
        
        # Check diagnoses for risk factors
        diagnoses = key_info.get("diagnoses", [])
        high_risk_conditions = ["diabetes", "hypertension", "cancer", "stroke", "heart disease"]
        
        for diagnosis in diagnoses:
            diagnosis_lower = str(diagnosis).lower()
            for condition in high_risk_conditions:
                if condition in diagnosis_lower:
                    risk_factors.append(f"History of {condition}")
        
        # Check medications for risk indicators
        medications = key_info.get("medications", [])
        for med in medications:
            med_lower = str(med).lower()
            if any(term in med_lower for term in ["chemotherapy", "radiation", "immunosuppressant"]):
                risk_factors.append("Immunocompromised state")
        
        return list(set(risk_factors))  # Remove duplicates
    
    def _generate_overall_summary(self, processed_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate overall patient summary."""
        key_info = processed_data.get("processed_data", {}).get("key_information", {})
        
        summary = {
            "patient_id": processed_data["patient_id"],
            "primary_diagnoses": key_info.get("diagnoses", [])[:3],  # Top 3
            "current_medications": key_info.get("medications", [])[:5],  # Top 5
            "known_allergies": key_info.get("allergies", []),
            "recent_imaging": [],
            "risk_factors": processed_data.get("processed_data", {}).get("risk_factors", []),
            "summary_text": ""
        }
        
        # Add recent imaging
        if processed_data["raw_data"].get("imaging_reports"):
            recent_imaging = processed_data["raw_data"]["imaging_reports"][-3:]  # Last 3
            summary["recent_imaging"] = [
                {
                    "modality": report["modality"],
                    "date": report.get("date"),
                    "key_findings": report.get("findings", [])[:2]  # Top 2 findings
                }
                for report in recent_imaging
            ]
        
        # Generate summary text
        summary_parts = []
        
        if summary["primary_diagnoses"]:
            summary_parts.append(f"Primary diagnoses: {', '.join(map(str, summary['primary_diagnoses']))}")
        
        if summary["current_medications"]:
            summary_parts.append(f"Current medications: {', '.join(map(str, summary['current_medications']))}")
        
        if summary["known_allergies"]:
            summary_parts.append(f"Known allergies: {', '.join(map(str, summary['known_allergies']))}")
        
        if summary["risk_factors"]:
            summary_parts.append(f"Risk factors: {', '.join(summary['risk_factors'])}")
        
        summary["summary_text"] = ". ".join(summary_parts)
        
        return summary
    
    def prepare_for_llm(self, processed_data: Dict[str, Any]) -> List[Document]:
        """
        Prepare patient data for LLM processing.
        
        Args:
            processed_data: Processed patient data
            
        Returns:
            List of LangChain Document objects
        """
        documents = []
        
        # Create document from overall summary
        summary = processed_data.get("summary", {})
        if summary.get("summary_text"):
            documents.append(Document(
                page_content=summary["summary_text"],
                metadata={
                    "patient_id": processed_data["patient_id"],
                    "document_type": "summary",
                    "source": "processed_summary"
                }
            ))
        
        # Create documents from text summaries
        summaries = processed_data.get("processed_data", {}).get("summaries", {})
        for summary_type, summary_text in summaries.items():
            if summary_text:
                documents.append(Document(
                    page_content=summary_text,
                    metadata={
                        "patient_id": processed_data["patient_id"],
                        "document_type": summary_type,
                        "source": f"{summary_type}_summary"
                    }
                ))
        
        # Create documents from original documents (chunked)
        if processed_data["raw_data"].get("documents"):
            for doc in processed_data["raw_data"]["documents"]:
                chunks = self.text_splitter.split_text(doc["content"])
                for i, chunk in enumerate(chunks):
                    documents.append(Document(
                        page_content=chunk,
                        metadata={
                            "patient_id": processed_data["patient_id"],
                            "document_type": doc["type"],
                            "source": doc["filename"],
                            "chunk_index": i
                        }
                    ))
        
        logger.info(f"Prepared {len(documents)} documents for LLM processing")
        return documents
