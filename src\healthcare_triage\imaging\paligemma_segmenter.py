"""
PaliGemma 2 Segmenter for brain tumor segmentation.
"""

import logging
from typing import Dict, List, Optional, Tuple, Union
from pathlib import Path

import torch
import torch.nn as nn
import numpy as np
from transformers import (
    PaliGemmaForConditionalGeneration,
    PaliGemmaProcessor,
    AutoTokenizer,
    TrainingArguments,
    Trainer
)
from PIL import Image
import matplotlib.pyplot as plt

from ..utils.config import Config

logger = logging.getLogger(__name__)


class PaliGemmaSegmenter:
    """
    PaliGemma 2 model for brain MRI tumor segmentation.
    
    This class handles:
    - Loading and fine-tuning PaliGemma 2 for segmentation
    - Converting MRI volumes to 2D slices for processing
    - Generating segmentation masks
    - Post-processing and visualization
    """
    
    def __init__(self, config: Config, model_name: Optional[str] = None):
        """
        Initialize PaliGemma segmenter.
        
        Args:
            config: Configuration object
            model_name: Optional model name override
        """
        self.config = config
        self.model_name = model_name or config.models.paligemma.model_name
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        # Initialize model and processor
        self.model = None
        self.processor = None
        self.tokenizer = None
        
        self._load_model()
        
    def _load_model(self):
        """Load PaliGemma 2 model and processor."""
        try:
            logger.info(f"Loading PaliGemma 2 model: {self.model_name}")
            
            # Load processor and tokenizer
            self.processor = PaliGemmaProcessor.from_pretrained(self.model_name)
            self.tokenizer = AutoTokenizer.from_pretrained(self.model_name)
            
            # Load model
            self.model = PaliGemmaForConditionalGeneration.from_pretrained(
                self.model_name,
                torch_dtype=torch.float16 if self.device.type == "cuda" else torch.float32,
                device_map="auto" if self.device.type == "cuda" else None
            )
            
            # Move to device if not using device_map
            if self.device.type != "cuda":
                self.model = self.model.to(self.device)
                
            logger.info("Model loaded successfully")
            
        except Exception as e:
            logger.error(f"Error loading model: {e}")
            raise
    
    def prepare_image_for_model(self, image: Union[torch.Tensor, np.ndarray]) -> Image.Image:
        """
        Convert MRI tensor/array to PIL Image for PaliGemma processing.
        
        Args:
            image: MRI image tensor or numpy array
            
        Returns:
            PIL Image object
        """
        if isinstance(image, torch.Tensor):
            image = image.cpu().numpy()
        
        # Handle different dimensions
        if image.ndim == 4:  # Batch dimension
            image = image[0]
        if image.ndim == 3 and image.shape[0] == 1:  # Channel dimension
            image = image[0]
        elif image.ndim == 3:  # 3D volume - take middle slice
            image = image[image.shape[0] // 2]
            
        # Normalize to 0-255 range
        image = ((image - image.min()) / (image.max() - image.min()) * 255).astype(np.uint8)
        
        # Convert to RGB (duplicate grayscale across channels)
        if image.ndim == 2:
            image = np.stack([image, image, image], axis=-1)
            
        return Image.fromarray(image)
    
    def segment_image(
        self, 
        image: Union[torch.Tensor, np.ndarray, Image.Image],
        prompt: str = "Segment the brain tumor in this MRI image"
    ) -> Dict:
        """
        Perform tumor segmentation on MRI image.
        
        Args:
            image: Input MRI image
            prompt: Text prompt for segmentation
            
        Returns:
            Dictionary containing segmentation results
        """
        # Prepare image
        if not isinstance(image, Image.Image):
            pil_image = self.prepare_image_for_model(image)
        else:
            pil_image = image
            
        # Process inputs
        inputs = self.processor(
            text=prompt,
            images=pil_image,
            return_tensors="pt"
        ).to(self.device)
        
        # Generate segmentation
        with torch.no_grad():
            outputs = self.model.generate(
                **inputs,
                max_length=self.config.models.paligemma.max_length,
                do_sample=False,
                temperature=0.1
            )
        
        # Decode output
        generated_text = self.processor.decode(outputs[0], skip_special_tokens=True)
        
        # Extract segmentation information from generated text
        # This is a simplified approach - in practice, you'd need more sophisticated parsing
        segmentation_info = self._parse_segmentation_output(generated_text)
        
        return {
            "generated_text": generated_text,
            "segmentation_info": segmentation_info,
            "input_image": pil_image
        }
    
    def _parse_segmentation_output(self, generated_text: str) -> Dict:
        """
        Parse segmentation information from generated text.
        
        Args:
            generated_text: Generated text from model
            
        Returns:
            Dictionary with parsed segmentation information
        """
        # This is a placeholder implementation
        # In practice, you'd implement sophisticated parsing based on your training format
        
        info = {
            "has_tumor": "tumor" in generated_text.lower(),
            "tumor_type": None,
            "location": None,
            "size": None,
            "confidence": 0.0
        }
        
        # Extract tumor type
        tumor_types = ["glioma", "meningioma", "adenoma", "metastasis"]
        for tumor_type in tumor_types:
            if tumor_type in generated_text.lower():
                info["tumor_type"] = tumor_type
                break
        
        # Extract location information
        locations = ["frontal", "parietal", "temporal", "occipital", "cerebellum", "brainstem"]
        for location in locations:
            if location in generated_text.lower():
                info["location"] = location
                break
                
        return info
    
    def fine_tune(
        self,
        train_dataset,
        val_dataset,
        output_dir: str,
        num_epochs: int = None
    ):
        """
        Fine-tune PaliGemma 2 model on brain MRI segmentation data.
        
        Args:
            train_dataset: Training dataset
            val_dataset: Validation dataset
            output_dir: Directory to save fine-tuned model
            num_epochs: Number of training epochs
        """
        if num_epochs is None:
            num_epochs = self.config.models.paligemma.num_epochs
            
        # Set up training arguments
        training_args = TrainingArguments(
            output_dir=output_dir,
            num_train_epochs=num_epochs,
            per_device_train_batch_size=self.config.models.paligemma.batch_size,
            per_device_eval_batch_size=self.config.models.paligemma.batch_size,
            learning_rate=self.config.models.paligemma.learning_rate,
            warmup_steps=self.config.training.warmup_steps,
            logging_steps=50,
            save_steps=self.config.training.save_steps,
            eval_steps=self.config.training.eval_steps,
            evaluation_strategy="steps",
            save_strategy="steps",
            load_best_model_at_end=True,
            metric_for_best_model="eval_loss",
            greater_is_better=False,
            fp16=self.config.training.mixed_precision and self.device.type == "cuda",
            dataloader_pin_memory=False,
            remove_unused_columns=False,
        )
        
        # Initialize trainer
        trainer = Trainer(
            model=self.model,
            args=training_args,
            train_dataset=train_dataset,
            eval_dataset=val_dataset,
            tokenizer=self.processor.tokenizer,
        )
        
        # Start training
        logger.info("Starting fine-tuning...")
        trainer.train()
        
        # Save final model
        trainer.save_model()
        logger.info(f"Fine-tuning completed. Model saved to {output_dir}")
    
    def visualize_segmentation(
        self,
        image: Union[torch.Tensor, np.ndarray, Image.Image],
        segmentation_result: Dict,
        save_path: Optional[str] = None
    ):
        """
        Visualize segmentation results.
        
        Args:
            image: Original MRI image
            segmentation_result: Result from segment_image()
            save_path: Optional path to save visualization
        """
        fig, axes = plt.subplots(1, 2, figsize=(12, 6))
        
        # Original image
        if isinstance(image, Image.Image):
            axes[0].imshow(image)
        else:
            if isinstance(image, torch.Tensor):
                image = image.cpu().numpy()
            if image.ndim == 3:
                image = image[0] if image.shape[0] == 1 else image[image.shape[0] // 2]
            axes[0].imshow(image, cmap='gray')
        axes[0].set_title("Original MRI")
        axes[0].axis('off')
        
        # Segmentation info
        info_text = f"Generated Text: {segmentation_result['generated_text']}\n\n"
        seg_info = segmentation_result['segmentation_info']
        info_text += f"Has Tumor: {seg_info['has_tumor']}\n"
        info_text += f"Tumor Type: {seg_info['tumor_type']}\n"
        info_text += f"Location: {seg_info['location']}"
        
        axes[1].text(0.1, 0.5, info_text, transform=axes[1].transAxes, 
                    fontsize=10, verticalalignment='center', wrap=True)
        axes[1].set_title("Segmentation Results")
        axes[1].axis('off')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"Visualization saved to {save_path}")
        
        plt.show()
    
    def save_model(self, save_path: str):
        """Save the fine-tuned model."""
        self.model.save_pretrained(save_path)
        self.processor.save_pretrained(save_path)
        logger.info(f"Model saved to {save_path}")
    
    def load_model(self, model_path: str):
        """Load a fine-tuned model."""
        self.model = PaliGemmaForConditionalGeneration.from_pretrained(model_path)
        self.processor = PaliGemmaProcessor.from_pretrained(model_path)
        self.model = self.model.to(self.device)
        logger.info(f"Model loaded from {model_path}")
