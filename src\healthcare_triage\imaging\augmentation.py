"""
Data augmentation for MRI images using MONAI.
"""

import logging
from typing import Dict, List, Optional, Tuple, Union

import torch
import numpy as np
from monai.transforms import (
    Compose, RandFlipd, RandRotate90d, RandRotated,
    RandZoomd, RandShiftIntensityd, RandGaussianNoised,
    RandGaussianSmoothd, RandScaleIntensityd,
    RandAdjustContrastd, RandBiasFieldd,
    RandAffined
)

# Try to import RandElasticDeformd, use alternative if not available
try:
    from monai.transforms import RandElasticDeformd
    HAS_ELASTIC_DEFORM = True
except ImportError:
    HAS_ELASTIC_DEFORM = False

from ..utils.config import Config

logger = logging.getLogger(__name__)


class MRIAugmentation:
    """
    Data augmentation pipeline for MRI images.
    
    Provides various augmentation techniques:
    - Geometric transformations
    - Intensity augmentations
    - Noise injection
    - Elastic deformations
    """
    
    def __init__(self, config: Config):
        """
        Initialize augmentation pipeline.
        
        Args:
            config: Configuration object
        """
        self.config = config
        self.setup_augmentations()
        
    def setup_augmentations(self):
        """Set up different augmentation pipelines."""
        
        # Light augmentation for validation
        self.light_augmentation = Compose([
            RandFlipd(
                keys=["image", "label"],
                spatial_axis=[0, 1, 2],
                prob=0.3
            ),
            RandRotate90d(
                keys=["image", "label"],
                prob=0.2,
                max_k=3
            ),
        ])
        
        # Medium augmentation for training
        self.medium_augmentation = Compose([
            RandFlipd(
                keys=["image", "label"],
                spatial_axis=[0, 1, 2],
                prob=self.config.preprocessing.augmentation.flip_probability
            ),
            RandRotated(
                keys=["image", "label"],
                range_x=np.radians(self.config.preprocessing.augmentation.rotation_range),
                range_y=np.radians(self.config.preprocessing.augmentation.rotation_range),
                range_z=np.radians(self.config.preprocessing.augmentation.rotation_range),
                prob=0.4,
                mode=("bilinear", "nearest"),
                padding_mode="border"
            ),
            RandZoomd(
                keys=["image", "label"],
                min_zoom=1.0 - self.config.preprocessing.augmentation.zoom_range,
                max_zoom=1.0 + self.config.preprocessing.augmentation.zoom_range,
                prob=0.3,
                mode=("bilinear", "nearest"),
                padding_mode="constant"
            ),
            RandShiftIntensityd(
                keys=["image"],
                offsets=0.1,
                prob=0.5
            ),
            RandScaleIntensityd(
                keys=["image"],
                factors=0.1,
                prob=0.3
            ),
        ])
        
        # Heavy augmentation for training with limited data
        self.heavy_augmentation = Compose([
            RandFlipd(
                keys=["image", "label"],
                spatial_axis=[0, 1, 2],
                prob=0.5
            ),
            RandRotated(
                keys=["image", "label"],
                range_x=np.radians(20),
                range_y=np.radians(20),
                range_z=np.radians(20),
                prob=0.6,
                mode=("bilinear", "nearest"),
                padding_mode="border"
            ),
            RandAffined(
                keys=["image", "label"],
                prob=0.4,
                rotate_range=(np.radians(15), np.radians(15), np.radians(15)),
                scale_range=(0.1, 0.1, 0.1),
                translate_range=(10, 10, 10),
                mode=("bilinear", "nearest"),
                padding_mode="border"
            ),
            # Add elastic deformation if available
            *([] if not HAS_ELASTIC_DEFORM else [RandElasticDeformd(
                keys=["image", "label"],
                sigma_range=(5, 8),
                magnitude_range=(100, 200),
                prob=0.3,
                mode=("bilinear", "nearest"),
                padding_mode="border"
            )]),
            RandZoomd(
                keys=["image", "label"],
                min_zoom=0.8,
                max_zoom=1.2,
                prob=0.4,
                mode=("bilinear", "nearest"),
                padding_mode="constant"
            ),
            RandShiftIntensityd(
                keys=["image"],
                offsets=0.15,
                prob=0.6
            ),
            RandScaleIntensityd(
                keys=["image"],
                factors=0.15,
                prob=0.4
            ),
            RandAdjustContrastd(
                keys=["image"],
                gamma=(0.8, 1.2),
                prob=0.3
            ),
            RandGaussianNoised(
                keys=["image"],
                std=0.05,
                prob=0.3
            ),
            RandGaussianSmoothd(
                keys=["image"],
                sigma_x=(0.5, 1.0),
                sigma_y=(0.5, 1.0),
                sigma_z=(0.5, 1.0),
                prob=0.2
            ),
            RandBiasFieldd(
                keys=["image"],
                degree=3,
                coeff_range=(0.0, 0.1),
                prob=0.2
            ),
        ])
    
    def get_augmentation_pipeline(self, intensity: str = "medium") -> Compose:
        """
        Get augmentation pipeline based on intensity.
        
        Args:
            intensity: Augmentation intensity ("light", "medium", "heavy")
            
        Returns:
            MONAI Compose transform
        """
        if intensity == "light":
            return self.light_augmentation
        elif intensity == "medium":
            return self.medium_augmentation
        elif intensity == "heavy":
            return self.heavy_augmentation
        else:
            raise ValueError(f"Unknown augmentation intensity: {intensity}")
    
    def apply_geometric_augmentation(
        self,
        data_dict: Dict,
        flip_prob: float = 0.5,
        rotation_range: float = 15,
        zoom_range: float = 0.1
    ) -> Dict:
        """
        Apply geometric augmentations.
        
        Args:
            data_dict: Dictionary with "image" and "label" keys
            flip_prob: Probability of flipping
            rotation_range: Rotation range in degrees
            zoom_range: Zoom range (0.1 means ±10%)
            
        Returns:
            Augmented data dictionary
        """
        geometric_transforms = Compose([
            RandFlipd(
                keys=["image", "label"],
                spatial_axis=[0, 1, 2],
                prob=flip_prob
            ),
            RandRotated(
                keys=["image", "label"],
                range_x=np.radians(rotation_range),
                range_y=np.radians(rotation_range),
                range_z=np.radians(rotation_range),
                prob=0.5,
                mode=("bilinear", "nearest"),
                padding_mode="border"
            ),
            RandZoomd(
                keys=["image", "label"],
                min_zoom=1.0 - zoom_range,
                max_zoom=1.0 + zoom_range,
                prob=0.4,
                mode=("bilinear", "nearest"),
                padding_mode="constant"
            ),
        ])
        
        return geometric_transforms(data_dict)
    
    def apply_intensity_augmentation(
        self,
        data_dict: Dict,
        shift_intensity: bool = True,
        scale_intensity: bool = True,
        adjust_contrast: bool = True,
        add_noise: bool = True
    ) -> Dict:
        """
        Apply intensity augmentations.
        
        Args:
            data_dict: Dictionary with "image" key
            shift_intensity: Whether to apply intensity shifting
            scale_intensity: Whether to apply intensity scaling
            adjust_contrast: Whether to adjust contrast
            add_noise: Whether to add Gaussian noise
            
        Returns:
            Augmented data dictionary
        """
        intensity_transforms = []
        
        if shift_intensity:
            intensity_transforms.append(
                RandShiftIntensityd(
                    keys=["image"],
                    offsets=0.1,
                    prob=0.5
                )
            )
        
        if scale_intensity:
            intensity_transforms.append(
                RandScaleIntensityd(
                    keys=["image"],
                    factors=0.1,
                    prob=0.4
                )
            )
        
        if adjust_contrast:
            intensity_transforms.append(
                RandAdjustContrastd(
                    keys=["image"],
                    gamma=(0.8, 1.2),
                    prob=0.3
                )
            )
        
        if add_noise:
            intensity_transforms.append(
                RandGaussianNoised(
                    keys=["image"],
                    std=0.05,
                    prob=0.3
                )
            )
        
        if intensity_transforms:
            transform = Compose(intensity_transforms)
            return transform(data_dict)
        else:
            return data_dict
    
    def apply_elastic_deformation(
        self,
        data_dict: Dict,
        sigma_range: Tuple[float, float] = (5, 8),
        magnitude_range: Tuple[float, float] = (100, 200),
        prob: float = 0.3
    ) -> Dict:
        """
        Apply elastic deformation.

        Args:
            data_dict: Dictionary with "image" and "label" keys
            sigma_range: Range for sigma parameter
            magnitude_range: Range for magnitude parameter
            prob: Probability of applying deformation

        Returns:
            Augmented data dictionary
        """
        if not HAS_ELASTIC_DEFORM:
            self.logger.warning("RandElasticDeformd not available, skipping elastic deformation")
            return data_dict

        elastic_transform = RandElasticDeformd(
            keys=["image", "label"],
            sigma_range=sigma_range,
            magnitude_range=magnitude_range,
            prob=prob,
            mode=("bilinear", "nearest"),
            padding_mode="border"
        )

        return elastic_transform(data_dict)
    
    def create_custom_augmentation(
        self,
        geometric_prob: float = 0.5,
        intensity_prob: float = 0.4,
        noise_prob: float = 0.3,
        elastic_prob: float = 0.2
    ) -> Compose:
        """
        Create custom augmentation pipeline.
        
        Args:
            geometric_prob: Probability for geometric transforms
            intensity_prob: Probability for intensity transforms
            noise_prob: Probability for noise injection
            elastic_prob: Probability for elastic deformation
            
        Returns:
            Custom MONAI Compose transform
        """
        transforms = []
        
        # Geometric transforms
        if geometric_prob > 0:
            transforms.extend([
                RandFlipd(
                    keys=["image", "label"],
                    spatial_axis=[0, 1, 2],
                    prob=geometric_prob
                ),
                RandRotated(
                    keys=["image", "label"],
                    range_x=np.radians(15),
                    range_y=np.radians(15),
                    range_z=np.radians(15),
                    prob=geometric_prob * 0.8,
                    mode=("bilinear", "nearest"),
                    padding_mode="border"
                ),
            ])
        
        # Intensity transforms
        if intensity_prob > 0:
            transforms.extend([
                RandShiftIntensityd(
                    keys=["image"],
                    offsets=0.1,
                    prob=intensity_prob
                ),
                RandScaleIntensityd(
                    keys=["image"],
                    factors=0.1,
                    prob=intensity_prob * 0.8
                ),
            ])
        
        # Noise injection
        if noise_prob > 0:
            transforms.append(
                RandGaussianNoised(
                    keys=["image"],
                    std=0.05,
                    prob=noise_prob
                )
            )
        
        # Elastic deformation (if available)
        if elastic_prob > 0 and HAS_ELASTIC_DEFORM:
            transforms.append(
                RandElasticDeformd(
                    keys=["image", "label"],
                    sigma_range=(5, 8),
                    magnitude_range=(100, 200),
                    prob=elastic_prob,
                    mode=("bilinear", "nearest"),
                    padding_mode="border"
                )
            )
        
        return Compose(transforms)
    
    def visualize_augmentation(
        self,
        original_data: Dict,
        augmented_data: Dict,
        slice_idx: Optional[int] = None
    ):
        """
        Visualize original vs augmented data.
        
        Args:
            original_data: Original data dictionary
            augmented_data: Augmented data dictionary
            slice_idx: Slice index to visualize (middle slice if None)
        """
        import matplotlib.pyplot as plt
        
        # Get images
        orig_img = original_data["image"]
        aug_img = augmented_data["image"]
        
        if isinstance(orig_img, torch.Tensor):
            orig_img = orig_img.cpu().numpy()
        if isinstance(aug_img, torch.Tensor):
            aug_img = aug_img.cpu().numpy()
        
        # Handle dimensions
        if orig_img.ndim == 4:  # Remove batch dimension
            orig_img = orig_img[0]
            aug_img = aug_img[0]
        if orig_img.ndim == 3 and orig_img.shape[0] == 1:  # Remove channel dimension
            orig_img = orig_img[0]
            aug_img = aug_img[0]
        
        # Select slice
        if slice_idx is None:
            slice_idx = orig_img.shape[0] // 2 if orig_img.ndim == 3 else 0
        
        if orig_img.ndim == 3:
            orig_slice = orig_img[slice_idx]
            aug_slice = aug_img[slice_idx]
        else:
            orig_slice = orig_img
            aug_slice = aug_img
        
        # Plot
        fig, axes = plt.subplots(1, 2, figsize=(12, 6))
        
        axes[0].imshow(orig_slice, cmap='gray')
        axes[0].set_title("Original")
        axes[0].axis('off')
        
        axes[1].imshow(aug_slice, cmap='gray')
        axes[1].set_title("Augmented")
        axes[1].axis('off')
        
        plt.tight_layout()
        plt.show()
        
        logger.info("Augmentation visualization displayed")
