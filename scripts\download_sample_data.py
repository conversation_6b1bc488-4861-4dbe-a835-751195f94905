#!/usr/bin/env python3
"""
Download sample datasets for Healthcare Multimodal Triage Agent.

This script helps users download and set up sample datasets for training and testing.
"""

import os
import sys
import logging
import requests
import zipfile
import tarfile
from pathlib import Path
from typing import List, Dict, Optional
import argparse

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from healthcare_triage.utils.config import load_config


def setup_logging():
    """Set up logging for the download script."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('download.log')
        ]
    )
    return logging.getLogger(__name__)


class DatasetDownloader:
    """Class to handle dataset downloads and setup."""
    
    def __init__(self, config):
        """Initialize the downloader."""
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Dataset information
        self.datasets = {
            "brats2021": {
                "name": "BraTS 2021 Challenge Dataset",
                "description": "Brain Tumor Segmentation Challenge 2021",
                "url": "https://www.med.upenn.edu/cbica/brats2021/data.html",
                "type": "registration_required",
                "size": "~7GB",
                "format": "NIfTI (.nii.gz)"
            },
            "sample_mri": {
                "name": "Sample MRI Data",
                "description": "Small sample dataset for testing",
                "url": "synthetic",  # We'll create synthetic data
                "type": "synthetic",
                "size": "~50MB",
                "format": "NIfTI (.nii.gz)"
            },
            "medical_knowledge": {
                "name": "Medical Knowledge Base",
                "description": "Sample medical knowledge for RAG system",
                "url": "synthetic",
                "type": "synthetic", 
                "size": "~10MB",
                "format": "Text files"
            }
        }
    
    def list_available_datasets(self):
        """List all available datasets."""
        self.logger.info("Available datasets:")
        print("\nAvailable Datasets:")
        print("=" * 50)
        
        for key, dataset in self.datasets.items():
            print(f"\nDataset: {key}")
            print(f"Name: {dataset['name']}")
            print(f"Description: {dataset['description']}")
            print(f"Size: {dataset['size']}")
            print(f"Format: {dataset['format']}")
            print(f"Type: {dataset['type']}")
    
    def download_dataset(self, dataset_key: str, force: bool = False):
        """Download a specific dataset."""
        if dataset_key not in self.datasets:
            raise ValueError(f"Unknown dataset: {dataset_key}")
        
        dataset = self.datasets[dataset_key]
        self.logger.info(f"Downloading dataset: {dataset['name']}")
        
        if dataset["type"] == "registration_required":
            self._handle_registration_required(dataset_key, dataset)
        elif dataset["type"] == "synthetic":
            self._create_synthetic_data(dataset_key, dataset)
        else:
            self._download_direct(dataset_key, dataset, force)
    
    def _handle_registration_required(self, key: str, dataset: Dict):
        """Handle datasets that require registration."""
        self.logger.info(f"Dataset {key} requires manual registration")
        
        print(f"\n{dataset['name']} requires manual download:")
        print(f"1. Visit: {dataset['url']}")
        print("2. Register and download the dataset")
        print("3. Extract to the appropriate data directory")
        
        if key == "brats2021":
            print("\nFor BraTS 2021:")
            print("- Extract training data to: data/mri/train/")
            print("- Extract validation data to: data/mri/val/")
            print("- Organize as: images/ and labels/ subdirectories")
            print("- Ensure files are in .nii.gz format")
    
    def _create_synthetic_data(self, key: str, dataset: Dict):
        """Create synthetic data for testing."""
        self.logger.info(f"Creating synthetic data for {key}")
        
        if key == "sample_mri":
            self._create_synthetic_mri()
        elif key == "medical_knowledge":
            self._create_medical_knowledge_base()
    
    def _create_synthetic_mri(self):
        """Create synthetic MRI data for testing."""
        import numpy as np
        import nibabel as nib
        
        # Create directories
        for split in ["train", "val", "test"]:
            for subdir in ["images", "labels"]:
                Path(f"data/mri/{split}/{subdir}").mkdir(parents=True, exist_ok=True)
        
        # Generate synthetic data
        for split in ["train", "val", "test"]:
            num_samples = {"train": 10, "val": 3, "test": 2}[split]
            
            for i in range(num_samples):
                # Create synthetic brain image (128x128x128)
                brain_image = self._generate_synthetic_brain(128, 128, 128)
                
                # Create synthetic tumor mask
                tumor_mask = self._generate_synthetic_tumor_mask(128, 128, 128)
                
                # Save as NIfTI files
                patient_id = f"patient_{split}_{i:03d}"
                
                # Save image
                img_nii = nib.Nifti1Image(brain_image, np.eye(4))
                img_path = f"data/mri/{split}/images/{patient_id}_image.nii.gz"
                nib.save(img_nii, img_path)
                
                # Save label
                label_nii = nib.Nifti1Image(tumor_mask.astype(np.uint8), np.eye(4))
                label_path = f"data/mri/{split}/labels/{patient_id}_label.nii.gz"
                nib.save(label_nii, label_path)
                
                self.logger.info(f"Created synthetic sample: {patient_id}")
        
        self.logger.info("Synthetic MRI data creation completed")
    
    def _generate_synthetic_brain(self, height: int, width: int, depth: int) -> np.ndarray:
        """Generate synthetic brain MRI image."""
        # Create basic brain-like structure
        brain = np.zeros((height, width, depth), dtype=np.float32)
        
        # Create brain outline (ellipsoid)
        center = (height // 2, width // 2, depth // 2)
        for i in range(height):
            for j in range(width):
                for k in range(depth):
                    # Ellipsoid equation
                    dist = ((i - center[0]) / (height * 0.4)) ** 2 + \
                           ((j - center[1]) / (width * 0.4)) ** 2 + \
                           ((k - center[2]) / (depth * 0.4)) ** 2
                    
                    if dist <= 1:
                        # Inside brain
                        brain[i, j, k] = 0.7 + 0.3 * np.random.random()
                    else:
                        # Outside brain (background)
                        brain[i, j, k] = 0.1 * np.random.random()
        
        # Add some noise
        noise = np.random.normal(0, 0.05, brain.shape)
        brain = np.clip(brain + noise, 0, 1)
        
        # Scale to typical MRI intensity range
        brain = brain * 1000
        
        return brain
    
    def _generate_synthetic_tumor_mask(self, height: int, width: int, depth: int) -> np.ndarray:
        """Generate synthetic tumor segmentation mask."""
        mask = np.zeros((height, width, depth), dtype=bool)
        
        # Randomly place 1-3 tumors
        num_tumors = np.random.randint(1, 4)
        
        for _ in range(num_tumors):
            # Random tumor center (within brain region)
            center_i = np.random.randint(height // 4, 3 * height // 4)
            center_j = np.random.randint(width // 4, 3 * width // 4)
            center_k = np.random.randint(depth // 4, 3 * depth // 4)
            
            # Random tumor size
            radius_i = np.random.randint(5, 15)
            radius_j = np.random.randint(5, 15)
            radius_k = np.random.randint(5, 15)
            
            # Create ellipsoidal tumor
            for i in range(max(0, center_i - radius_i), min(height, center_i + radius_i)):
                for j in range(max(0, center_j - radius_j), min(width, center_j + radius_j)):
                    for k in range(max(0, center_k - radius_k), min(depth, center_k + radius_k)):
                        dist = ((i - center_i) / radius_i) ** 2 + \
                               ((j - center_j) / radius_j) ** 2 + \
                               ((k - center_k) / radius_k) ** 2
                        
                        if dist <= 1:
                            mask[i, j, k] = True
        
        return mask
    
    def _create_medical_knowledge_base(self):
        """Create sample medical knowledge base."""
        knowledge_dir = Path("data/knowledge_base")
        knowledge_dir.mkdir(parents=True, exist_ok=True)
        
        # Sample medical knowledge documents
        documents = {
            "brain_tumors_overview.txt": """
Brain Tumors: Clinical Overview

Brain tumors are abnormal growths of cells within the brain or central nervous system. They can be classified as:

1. Primary Brain Tumors:
   - Gliomas (most common): Include glioblastoma, astrocytoma, oligodendroglioma
   - Meningiomas: Arise from meninges
   - Pituitary adenomas: Benign tumors of pituitary gland
   - Schwannomas: Nerve sheath tumors

2. Secondary (Metastatic) Brain Tumors:
   - Spread from other parts of the body
   - Common primary sites: lung, breast, kidney, melanoma

Symptoms may include:
- Headaches (especially morning headaches)
- Seizures
- Cognitive changes
- Motor weakness
- Visual disturbances
- Speech difficulties

Diagnosis typically involves:
- Neurological examination
- MRI with and without contrast
- CT scan
- Biopsy for definitive diagnosis

Treatment options:
- Surgery (when feasible)
- Radiation therapy
- Chemotherapy
- Targeted therapy
- Supportive care
""",
            
            "mri_interpretation.txt": """
MRI Brain Interpretation Guidelines

Key Sequences:
1. T1-weighted: Good for anatomy, post-contrast enhancement
2. T2-weighted: Shows edema, CSF bright
3. FLAIR: Suppresses CSF signal, good for periventricular lesions
4. DWI: Detects acute ischemia, abscesses
5. T1 post-contrast: Shows blood-brain barrier breakdown

Common Findings:

Glioblastoma:
- Heterogeneous enhancement
- Central necrosis
- Surrounding edema
- Mass effect
- Restricted diffusion in cellular areas

Meningioma:
- Homogeneous enhancement
- "Dural tail" sign
- Well-circumscribed
- May have calcifications

Metastases:
- Multiple lesions (often)
- Ring enhancement
- Significant edema
- Gray-white junction location

Reporting Elements:
- Location and size
- Enhancement pattern
- Mass effect
- Edema extent
- Midline shift
- Hydrocephalus
""",
            
            "triage_guidelines.txt": """
Emergency Department Triage Guidelines for Neurological Patients

Level 1 (Emergency - Immediate):
- Altered mental status with vital sign instability
- Acute stroke symptoms within treatment window
- Status epilepticus
- Signs of increased intracranial pressure
- Severe head trauma with neurological deficits

Level 2 (Urgent - 15-60 minutes):
- New onset seizures
- Severe headache with neurological signs
- Acute vision loss
- Suspected meningitis
- Moderate head trauma

Level 3 (Less Urgent - 1-4 hours):
- Chronic headache with new features
- Mild neurological symptoms
- Stable post-ictal patients
- Minor head trauma without loss of consciousness

Level 4 (Non-Urgent - 4+ hours):
- Chronic stable neurological conditions
- Medication refills
- Routine follow-up concerns
- Minor headaches without red flags

Red Flags Requiring Immediate Attention:
- Sudden severe headache ("worst headache of life")
- Headache with fever and neck stiffness
- Headache with visual changes or weakness
- Progressive neurological deficits
- Altered consciousness
"""
        }
        
        # Write documents
        for filename, content in documents.items():
            file_path = knowledge_dir / filename
            with open(file_path, 'w') as f:
                f.write(content.strip())
            self.logger.info(f"Created knowledge document: {filename}")
        
        # Create patient record samples
        self._create_sample_patient_records()
        
        self.logger.info("Medical knowledge base creation completed")
    
    def _create_sample_patient_records(self):
        """Create sample patient records."""
        records_dir = Path("data/patient_records")
        
        # Create subdirectories
        for source in ["ehr", "documents", "lab_reports", "imaging_reports"]:
            (records_dir / source).mkdir(parents=True, exist_ok=True)
        
        # Sample EHR data
        import json
        sample_ehr = {
            "demographics": {
                "age": 65,
                "gender": "M",
                "race": "Caucasian"
            },
            "medical_history": [
                "Hypertension",
                "Type 2 Diabetes",
                "Hyperlipidemia"
            ],
            "medications": [
                "Lisinopril 10mg daily",
                "Metformin 1000mg twice daily",
                "Atorvastatin 20mg daily"
            ],
            "allergies": ["Penicillin"],
            "vital_signs": [
                {
                    "date": "2024-01-15",
                    "bp": "140/90",
                    "hr": 78,
                    "temp": 98.6,
                    "resp": 16
                }
            ],
            "diagnoses": [
                "Essential hypertension",
                "Type 2 diabetes mellitus",
                "Mixed hyperlipidemia"
            ]
        }
        
        with open(records_dir / "ehr" / "patient_001_ehr.json", 'w') as f:
            json.dump(sample_ehr, f, indent=2)
        
        # Sample imaging report
        sample_report = """
BRAIN MRI WITH AND WITHOUT CONTRAST

CLINICAL HISTORY: 65-year-old male with headaches and cognitive changes.

TECHNIQUE: Multiplanar, multisequence MRI of the brain was performed with and without gadolinium contrast.

FINDINGS:
- There is a heterogeneously enhancing mass in the right frontal lobe measuring approximately 3.2 x 2.8 x 2.5 cm.
- The lesion demonstrates central necrosis with peripheral enhancement.
- Significant surrounding vasogenic edema is present.
- Mild mass effect with 3mm rightward midline shift.
- No evidence of hydrocephalus.
- Remaining brain parenchyma appears normal.

IMPRESSION:
1. Right frontal lobe mass with features concerning for high-grade glioma (glioblastoma).
2. Surrounding edema and mild mass effect.
3. Recommend neurosurgical consultation and consideration of biopsy.

Radiologist: Dr. Smith, MD
Date: January 15, 2024
"""
        
        patient_imaging_dir = records_dir / "imaging_reports" / "patient_001"
        patient_imaging_dir.mkdir(parents=True, exist_ok=True)
        
        with open(patient_imaging_dir / "mri_brain_20240115.txt", 'w') as f:
            f.write(sample_report.strip())
        
        self.logger.info("Sample patient records created")
    
    def _download_direct(self, key: str, dataset: Dict, force: bool):
        """Download dataset directly from URL."""
        # This would handle direct downloads
        # For now, just log that it's not implemented
        self.logger.info(f"Direct download not implemented for {key}")
    
    def verify_data_structure(self):
        """Verify that the data structure is correct."""
        self.logger.info("Verifying data structure...")
        
        required_dirs = [
            "data/mri/train/images",
            "data/mri/train/labels",
            "data/mri/val/images", 
            "data/mri/val/labels",
            "data/mri/test/images",
            "data/mri/test/labels",
            "data/patient_records/ehr",
            "data/patient_records/documents",
            "data/patient_records/lab_reports",
            "data/patient_records/imaging_reports",
            "data/knowledge_base"
        ]
        
        missing_dirs = []
        for directory in required_dirs:
            if not Path(directory).exists():
                missing_dirs.append(directory)
        
        if missing_dirs:
            self.logger.warning(f"Missing directories: {missing_dirs}")
            return False
        else:
            self.logger.info("Data structure verification passed")
            return True


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Download sample datasets for Healthcare Triage Agent")
    parser.add_argument("--list", action="store_true", help="List available datasets")
    parser.add_argument("--dataset", type=str, help="Download specific dataset")
    parser.add_argument("--all", action="store_true", help="Download all available datasets")
    parser.add_argument("--force", action="store_true", help="Force re-download")
    parser.add_argument("--verify", action="store_true", help="Verify data structure")
    
    args = parser.parse_args()
    
    logger = setup_logging()
    
    try:
        # Load configuration
        config = load_config()
        
        # Initialize downloader
        downloader = DatasetDownloader(config)
        
        if args.list:
            downloader.list_available_datasets()
        elif args.verify:
            downloader.verify_data_structure()
        elif args.dataset:
            downloader.download_dataset(args.dataset, args.force)
        elif args.all:
            for dataset_key in downloader.datasets.keys():
                try:
                    downloader.download_dataset(dataset_key, args.force)
                except Exception as e:
                    logger.error(f"Failed to download {dataset_key}: {e}")
        else:
            print("Use --help for usage information")
            downloader.list_available_datasets()
        
        logger.info("Dataset download script completed")
        
    except Exception as e:
        logger.error(f"Script failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
