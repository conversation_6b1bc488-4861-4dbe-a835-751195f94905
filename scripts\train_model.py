#!/usr/bin/env python3
"""
Training script for Healthcare Multimodal Triage Agent.

This script handles:
1. PaliGemma 2 fine-tuning for brain MRI segmentation
2. Model evaluation and validation
3. Checkpoint management
4. Training monitoring and logging
"""

import os
import sys
import logging
import argparse
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, Optional

import torch
import wandb
from torch.utils.tensorboard import SummaryWriter

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from healthcare_triage.imaging import MRIProcessor, PaliGemmaSegmenter
from healthcare_triage.utils.config import load_config, create_directories_from_config
from healthcare_triage.utils.logging_utils import setup_logging


class ModelTrainer:
    """Main training class for the Healthcare Triage Agent."""
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize the trainer.
        
        Args:
            config_path: Path to configuration file
        """
        self.config = load_config(config_path)
        self.logger = setup_logging(self.config)
        
        # Create necessary directories
        create_directories_from_config(self.config)
        
        # Initialize components
        self.mri_processor = MRIProcessor(self.config)
        self.segmenter = PaliGemmaSegmenter(self.config)
        
        # Training state
        self.current_epoch = 0
        self.best_dice_score = 0.0
        self.training_history = []
        
        # Set up monitoring
        self.setup_monitoring()
        
    def setup_monitoring(self):
        """Set up training monitoring and logging."""
        # Create run directory
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.run_dir = Path("runs") / f"training_{timestamp}"
        self.run_dir.mkdir(parents=True, exist_ok=True)
        
        # Set up TensorBoard
        self.tb_writer = SummaryWriter(log_dir=str(self.run_dir / "tensorboard"))
        
        # Set up Weights & Biases (optional)
        if os.getenv("WANDB_API_KEY"):
            wandb.init(
                project="healthcare-triage-agent",
                name=f"paligemma_training_{timestamp}",
                config=self.config.__dict__,
                dir=str(self.run_dir)
            )
            self.use_wandb = True
        else:
            self.use_wandb = False
            self.logger.info("WANDB_API_KEY not found, skipping W&B logging")
        
        self.logger.info(f"Training run directory: {self.run_dir}")
    
    def prepare_datasets(self):
        """Prepare training and validation datasets."""
        self.logger.info("Preparing datasets...")
        
        # Load datasets
        train_dataset = self.mri_processor.load_dataset(
            Path(self.config.data.mri_data_path) / "train",
            split="train"
        )
        
        val_dataset = self.mri_processor.load_dataset(
            Path(self.config.data.mri_data_path) / "val", 
            split="val"
        )
        
        # Create data loaders
        self.train_loader = self.mri_processor.create_dataloader(
            train_dataset,
            batch_size=self.config.models.paligemma.batch_size,
            shuffle=True
        )
        
        self.val_loader = self.mri_processor.create_dataloader(
            val_dataset,
            batch_size=self.config.models.paligemma.batch_size,
            shuffle=False
        )
        
        self.logger.info(f"Training samples: {len(train_dataset)}")
        self.logger.info(f"Validation samples: {len(val_dataset)}")
        
        return train_dataset, val_dataset
    
    def train_paligemma_segmentation(self):
        """Train PaliGemma 2 model for brain MRI segmentation."""
        self.logger.info("Starting PaliGemma 2 fine-tuning...")
        
        # Prepare datasets
        train_dataset, val_dataset = self.prepare_datasets()
        
        # Set up output directory
        output_dir = self.run_dir / "paligemma_checkpoints"
        output_dir.mkdir(exist_ok=True)
        
        try:
            # Fine-tune the model
            self.segmenter.fine_tune(
                train_dataset=train_dataset,
                val_dataset=val_dataset,
                output_dir=str(output_dir),
                num_epochs=self.config.models.paligemma.num_epochs
            )
            
            self.logger.info("PaliGemma 2 fine-tuning completed successfully")
            
            # Save final model
            final_model_dir = Path("models/paligemma/fine_tuned")
            final_model_dir.mkdir(parents=True, exist_ok=True)
            self.segmenter.save_model(str(final_model_dir))
            
            return True
            
        except Exception as e:
            self.logger.error(f"Training failed: {e}")
            return False
    
    def evaluate_model(self, test_dataset_path: Optional[str] = None):
        """Evaluate the trained model."""
        self.logger.info("Evaluating trained model...")
        
        if test_dataset_path is None:
            test_dataset_path = Path(self.config.data.mri_data_path) / "test"
        
        # Load test dataset
        test_dataset = self.mri_processor.load_dataset(test_dataset_path, split="test")
        test_loader = self.mri_processor.create_dataloader(
            test_dataset,
            batch_size=1,  # Process one at a time for evaluation
            shuffle=False
        )
        
        # Evaluation metrics
        total_dice_scores = []
        total_iou_scores = []
        
        self.logger.info(f"Evaluating on {len(test_dataset)} test samples...")
        
        for i, batch in enumerate(test_loader):
            try:
                # Get image and ground truth
                image = batch["image"][0]  # Remove batch dimension
                ground_truth = batch["label"][0] if "label" in batch else None
                
                # Perform segmentation
                result = self.segmenter.segment_image(image)
                
                # Calculate metrics if ground truth is available
                if ground_truth is not None:
                    # This is a simplified evaluation
                    # In practice, you'd need to convert the text output to segmentation mask
                    self.logger.info(f"Sample {i+1}: {result['generated_text'][:100]}...")
                
                # Save visualization
                viz_path = self.run_dir / "evaluations" / f"sample_{i+1:03d}.png"
                viz_path.parent.mkdir(exist_ok=True)
                
                self.segmenter.visualize_segmentation(
                    image,
                    result,
                    save_path=str(viz_path)
                )
                
            except Exception as e:
                self.logger.warning(f"Failed to evaluate sample {i+1}: {e}")
        
        self.logger.info("Model evaluation completed")
    
    def save_training_summary(self):
        """Save training summary and results."""
        summary = {
            "training_config": {
                "model_name": self.config.models.paligemma.model_name,
                "batch_size": self.config.models.paligemma.batch_size,
                "learning_rate": self.config.models.paligemma.learning_rate,
                "num_epochs": self.config.models.paligemma.num_epochs,
            },
            "training_results": {
                "completed_epochs": self.current_epoch,
                "best_dice_score": self.best_dice_score,
                "final_model_path": "models/paligemma/fine_tuned",
            },
            "system_info": {
                "cuda_available": torch.cuda.is_available(),
                "gpu_count": torch.cuda.device_count() if torch.cuda.is_available() else 0,
                "pytorch_version": torch.__version__,
            },
            "timestamp": datetime.now().isoformat(),
        }
        
        # Save summary
        import json
        summary_path = self.run_dir / "training_summary.json"
        with open(summary_path, 'w') as f:
            json.dump(summary, f, indent=2)
        
        self.logger.info(f"Training summary saved to {summary_path}")
    
    def cleanup(self):
        """Clean up resources."""
        if hasattr(self, 'tb_writer'):
            self.tb_writer.close()
        
        if self.use_wandb:
            wandb.finish()
        
        self.logger.info("Training cleanup completed")


def main():
    """Main training function."""
    parser = argparse.ArgumentParser(description="Train Healthcare Multimodal Triage Agent")
    parser.add_argument("--config", type=str, help="Path to configuration file")
    parser.add_argument("--eval-only", action="store_true", help="Only run evaluation")
    parser.add_argument("--model-path", type=str, help="Path to pre-trained model for evaluation")
    parser.add_argument("--test-data", type=str, help="Path to test dataset")
    parser.add_argument("--debug", action="store_true", help="Enable debug mode")
    
    args = parser.parse_args()
    
    try:
        # Initialize trainer
        trainer = ModelTrainer(args.config)
        
        if args.debug:
            trainer.logger.setLevel(logging.DEBUG)
        
        trainer.logger.info("Starting Healthcare Triage Agent training...")
        
        if args.eval_only:
            # Load pre-trained model if specified
            if args.model_path:
                trainer.segmenter.load_model(args.model_path)
            
            # Run evaluation only
            trainer.evaluate_model(args.test_data)
        else:
            # Full training pipeline
            success = trainer.train_paligemma_segmentation()
            
            if success:
                trainer.logger.info("Training completed successfully")
                
                # Run evaluation
                trainer.evaluate_model(args.test_data)
                
                # Save training summary
                trainer.save_training_summary()
                
                print("\n" + "="*60)
                print("Training Completed Successfully!")
                print("="*60)
                print(f"Model saved to: models/paligemma/fine_tuned")
                print(f"Training logs: {trainer.run_dir}")
                print(f"Visualizations: {trainer.run_dir}/evaluations")
                print("\nNext steps:")
                print("1. Review training logs and visualizations")
                print("2. Test the model with: python scripts/run_inference.py")
                print("3. Set up the full triage pipeline")
                
            else:
                trainer.logger.error("Training failed")
                sys.exit(1)
        
        # Cleanup
        trainer.cleanup()
        
    except KeyboardInterrupt:
        print("\nTraining interrupted by user")
        sys.exit(1)
    except Exception as e:
        logging.error(f"Training script failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
