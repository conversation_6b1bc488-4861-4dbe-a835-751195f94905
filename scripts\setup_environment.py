#!/usr/bin/env python3
"""
Environment setup script for Healthcare Multimodal Triage Agent.

This script:
1. Creates necessary directories
2. Downloads sample datasets
3. Sets up configuration files
4. Initializes logging
5. Validates the environment
"""

import os
import sys
import logging
import subprocess
from pathlib import Path
from typing import List, Optional

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from healthcare_triage.utils.config import Config, load_config, create_directories_from_config


def setup_logging():
    """Set up basic logging for setup script."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('setup.log')
        ]
    )
    return logging.getLogger(__name__)


def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 9):
        raise RuntimeError("Python 3.9 or higher is required")
    logging.info(f"Python version: {sys.version}")


def install_dependencies():
    """Install required dependencies."""
    logger = logging.getLogger(__name__)
    
    try:
        logger.info("Installing dependencies...")
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ])
        logger.info("Dependencies installed successfully")
    except subprocess.CalledProcessError as e:
        logger.error(f"Failed to install dependencies: {e}")
        raise


def create_project_directories(config: Config):
    """Create all necessary project directories."""
    logger = logging.getLogger(__name__)
    
    logger.info("Creating project directories...")
    create_directories_from_config(config)
    
    # Additional directories
    additional_dirs = [
        "data/raw",
        "data/interim", 
        "data/external",
        "models/pretrained",
        "outputs/segmentations",
        "outputs/reports",
        "outputs/visualizations",
        "logs/training",
        "logs/inference",
        "notebooks/experiments",
        "tests/data",
    ]
    
    for directory in additional_dirs:
        Path(directory).mkdir(parents=True, exist_ok=True)
        logger.info(f"Created directory: {directory}")


def download_sample_data():
    """Download sample datasets for testing."""
    logger = logging.getLogger(__name__)
    
    logger.info("Setting up sample data...")
    
    # Create sample data structure
    sample_dirs = [
        "data/mri/train/images",
        "data/mri/train/labels", 
        "data/mri/val/images",
        "data/mri/val/labels",
        "data/mri/test/images",
        "data/mri/test/labels",
        "data/patient_records/train",
        "data/patient_records/val",
        "data/patient_records/test",
    ]
    
    for directory in sample_dirs:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    # Create placeholder files
    readme_content = """# Sample Data Directory

This directory contains sample data for the Healthcare Multimodal Triage Agent.

## Structure:
- images/: MRI scan files (.nii.gz format)
- labels/: Corresponding segmentation masks (.nii.gz format)

## Data Sources:
For training, you should obtain data from:
1. BraTS Challenge: https://www.med.upenn.edu/cbica/brats2021/
2. TCIA: https://www.cancerimagingarchive.net/
3. Your institutional datasets (with proper approvals)

## Data Format:
- Images should be in NIfTI format (.nii.gz)
- Labels should be binary masks (0 = background, 1 = tumor)
- File naming convention: patient_id_modality.nii.gz

## Privacy and Compliance:
- Ensure all data is properly de-identified
- Obtain necessary IRB approvals
- Follow HIPAA/GDPR guidelines
"""
    
    for split in ["train", "val", "test"]:
        readme_path = f"data/mri/{split}/README.md"
        with open(readme_path, 'w') as f:
            f.write(readme_content)
    
    logger.info("Sample data structure created")


def setup_configuration():
    """Set up configuration files."""
    logger = logging.getLogger(__name__)
    
    logger.info("Setting up configuration...")
    
    # Load default config
    config = load_config()
    
    # Update paths to be absolute
    base_path = Path.cwd()
    config.data.mri_data_path = str(base_path / "data" / "mri")
    config.data.patient_records_path = str(base_path / "data" / "patient_records")
    config.data.knowledge_base_path = str(base_path / "data" / "knowledge_base")
    config.data.processed_data_path = str(base_path / "data" / "processed")
    
    # Save updated config
    config.to_yaml("configs/config_local.yaml")
    
    logger.info("Configuration files created")
    return config


def create_environment_file():
    """Create .env file for environment variables."""
    logger = logging.getLogger(__name__)
    
    env_content = """# Healthcare Triage Agent Environment Variables

# API Keys (replace with your actual keys)
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Paths
PROJECT_ROOT=.
DATA_PATH=./data
MODELS_PATH=./models
LOGS_PATH=./logs

# Database
QDRANT_HOST=localhost
QDRANT_PORT=6333

# Security
ENCRYPTION_KEY=your_encryption_key_here
JWT_SECRET=your_jwt_secret_here

# Compliance
HIPAA_AUDIT_LOG=./logs/hipaa_audit.log
GDPR_COMPLIANCE=true

# Development
DEBUG=false
LOG_LEVEL=INFO
"""
    
    with open('.env', 'w') as f:
        f.write(env_content)
    
    logger.info("Environment file created (.env)")


def validate_environment():
    """Validate that the environment is set up correctly."""
    logger = logging.getLogger(__name__)
    
    logger.info("Validating environment...")
    
    # Check critical directories
    critical_dirs = [
        "src/healthcare_triage",
        "configs",
        "data",
        "models",
        "logs",
    ]
    
    for directory in critical_dirs:
        if not Path(directory).exists():
            raise RuntimeError(f"Critical directory missing: {directory}")
    
    # Check configuration
    try:
        config = load_config("configs/config.yaml")
        logger.info("Configuration loaded successfully")
    except Exception as e:
        logger.error(f"Configuration validation failed: {e}")
        raise
    
    # Check Python imports
    try:
        import torch
        import monai
        import transformers
        import langchain
        logger.info("Core dependencies imported successfully")
    except ImportError as e:
        logger.error(f"Import validation failed: {e}")
        raise
    
    logger.info("Environment validation completed successfully")


def create_sample_scripts():
    """Create sample scripts for common tasks."""
    logger = logging.getLogger(__name__)
    
    # Training script
    train_script = """#!/usr/bin/env python3
\"\"\"
Sample training script for PaliGemma 2 fine-tuning.
\"\"\"

import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from healthcare_triage.imaging import MRIProcessor, PaliGemmaSegmenter
from healthcare_triage.utils.config import load_config

def main():
    # Load configuration
    config = load_config()
    
    # Initialize components
    mri_processor = MRIProcessor(config)
    segmenter = PaliGemmaSegmenter(config)
    
    # Load datasets
    train_dataset = mri_processor.load_dataset(config.data.mri_data_path + "/train", "train")
    val_dataset = mri_processor.load_dataset(config.data.mri_data_path + "/val", "val")
    
    # Fine-tune model
    segmenter.fine_tune(
        train_dataset=train_dataset,
        val_dataset=val_dataset,
        output_dir="models/paligemma/fine_tuned",
        num_epochs=config.models.paligemma.num_epochs
    )
    
    print("Training completed!")

if __name__ == "__main__":
    main()
"""
    
    with open("scripts/train_model.py", 'w') as f:
        f.write(train_script)
    
    # Inference script
    inference_script = """#!/usr/bin/env python3
\"\"\"
Sample inference script for brain MRI segmentation.
\"\"\"

import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from healthcare_triage.imaging import MRIProcessor, PaliGemmaSegmenter
from healthcare_triage.utils.config import load_config

def main():
    # Load configuration
    config = load_config()
    
    # Initialize components
    mri_processor = MRIProcessor(config)
    segmenter = PaliGemmaSegmenter(config)
    
    # Process single image
    image_path = "data/mri/test/images/sample.nii.gz"
    
    if Path(image_path).exists():
        # Preprocess image
        processed_image = mri_processor.preprocess_single_image(image_path)
        
        # Perform segmentation
        result = segmenter.segment_image(processed_image)
        
        # Visualize results
        segmenter.visualize_segmentation(
            processed_image, 
            result,
            save_path="outputs/segmentation_result.png"
        )
        
        print("Inference completed!")
    else:
        print(f"Sample image not found: {image_path}")
        print("Please add sample MRI data to test the inference pipeline.")

if __name__ == "__main__":
    main()
"""
    
    with open("scripts/run_inference.py", 'w') as f:
        f.write(inference_script)
    
    # Make scripts executable
    os.chmod("scripts/train_model.py", 0o755)
    os.chmod("scripts/run_inference.py", 0o755)
    
    logger.info("Sample scripts created")


def main():
    """Main setup function."""
    logger = setup_logging()
    
    try:
        logger.info("Starting Healthcare Triage Agent environment setup...")
        
        # Check Python version
        check_python_version()
        
        # Install dependencies
        install_dependencies()
        
        # Set up configuration
        config = setup_configuration()
        
        # Create directories
        create_project_directories(config)
        
        # Download sample data
        download_sample_data()
        
        # Create environment file
        create_environment_file()
        
        # Create sample scripts
        create_sample_scripts()
        
        # Validate environment
        validate_environment()
        
        logger.info("Environment setup completed successfully!")
        
        print("\n" + "="*60)
        print("Healthcare Multimodal Triage Agent Setup Complete!")
        print("="*60)
        print("\nNext steps:")
        print("1. Update .env file with your API keys")
        print("2. Add training data to data/mri/ directories")
        print("3. Run: python scripts/train_model.py")
        print("4. Test with: python scripts/run_inference.py")
        print("\nFor more information, see README.md")
        
    except Exception as e:
        logger.error(f"Setup failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
