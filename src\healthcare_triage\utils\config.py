"""
Configuration management for Healthcare Triage Agent.
"""

import os
import yaml
from typing import Any, Dict, Optional
from pathlib import Path
from dataclasses import dataclass, field
from omegaconf import OmegaConf, DictConfig


@dataclass
class PreprocessingConfig:
    """Preprocessing configuration."""
    image_size: list = field(default_factory=lambda: [224, 224, 224])
    spacing: list = field(default_factory=lambda: [1.0, 1.0, 1.0])
    intensity_range: list = field(default_factory=lambda: [0, 1])
    augmentation: Dict[str, Any] = field(default_factory=dict)


@dataclass
class PaliGemmaConfig:
    """PaliGemma model configuration."""
    model_name: str = "google/paligemma2-3b-pt-224"
    checkpoint_path: str = "models/paligemma/checkpoints"
    max_length: int = 512
    image_size: int = 224
    batch_size: int = 4
    learning_rate: float = 1e-5
    num_epochs: int = 10


@dataclass
class LLMConfig:
    """LLM configuration."""
    model_name: str = "gpt-4"
    temperature: float = 0.1
    max_tokens: int = 2048


@dataclass
class ModelsConfig:
    """Models configuration."""
    paligemma: PaliGemmaConfig = field(default_factory=PaliGemmaConfig)
    llm: LLMConfig = field(default_factory=LLMConfig)


@dataclass
class DataConfig:
    """Data paths configuration."""
    mri_data_path: str = "data/mri"
    patient_records_path: str = "data/patient_records"
    knowledge_base_path: str = "data/knowledge_base"
    processed_data_path: str = "data/processed"


@dataclass
class TrainingConfig:
    """Training configuration."""
    device: str = "cuda"
    mixed_precision: bool = True
    gradient_accumulation_steps: int = 4
    warmup_steps: int = 1000
    save_steps: int = 500
    eval_steps: int = 100


@dataclass
class VectorDBConfig:
    """Vector database configuration."""
    provider: str = "qdrant"
    host: str = "localhost"
    port: int = 6333
    collection_name: str = "medical_knowledge"
    embedding_model: str = "sentence-transformers/all-MiniLM-L6-v2"


@dataclass
class APIConfig:
    """API configuration."""
    host: str = "0.0.0.0"
    port: int = 8000
    debug: bool = False
    cors_origins: list = field(default_factory=lambda: ["*"])


@dataclass
class LoggingConfig:
    """Logging configuration."""
    level: str = "INFO"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    file: str = "logs/healthcare_triage.log"


@dataclass
class SecurityConfig:
    """Security and compliance configuration."""
    enable_encryption: bool = True
    hipaa_compliance: bool = True
    audit_logging: bool = True


@dataclass
class EvaluationConfig:
    """Evaluation metrics configuration."""
    segmentation_metrics: list = field(default_factory=lambda: ["dice_score", "iou", "sensitivity", "specificity"])
    nlp_metrics: list = field(default_factory=lambda: ["bleu", "rouge", "bertscore"])


@dataclass
class Config:
    """Main configuration class."""
    project: Dict[str, Any] = field(default_factory=dict)
    data: DataConfig = field(default_factory=DataConfig)
    models: ModelsConfig = field(default_factory=ModelsConfig)
    preprocessing: PreprocessingConfig = field(default_factory=PreprocessingConfig)
    training: TrainingConfig = field(default_factory=TrainingConfig)
    vector_db: VectorDBConfig = field(default_factory=VectorDBConfig)
    api: APIConfig = field(default_factory=APIConfig)
    logging: LoggingConfig = field(default_factory=LoggingConfig)
    security: SecurityConfig = field(default_factory=SecurityConfig)
    evaluation: EvaluationConfig = field(default_factory=EvaluationConfig)
    agents: Dict[str, Any] = field(default_factory=dict)

    @classmethod
    def from_yaml(cls, config_path: str) -> 'Config':
        """
        Load configuration from YAML file.
        
        Args:
            config_path: Path to YAML configuration file
            
        Returns:
            Config object
        """
        with open(config_path, 'r') as f:
            config_dict = yaml.safe_load(f)
        
        # Use OmegaConf for better handling of nested configs
        omega_config = OmegaConf.create(config_dict)
        
        # Convert to our dataclass structure
        config = cls()
        
        # Update fields from loaded config
        if 'project' in omega_config:
            config.project = dict(omega_config.project)
            
        if 'data' in omega_config:
            config.data = DataConfig(**omega_config.data)
            
        if 'models' in omega_config:
            models_dict = dict(omega_config.models)
            if 'paligemma' in models_dict:
                config.models.paligemma = PaliGemmaConfig(**models_dict['paligemma'])
            if 'llm' in models_dict:
                config.models.llm = LLMConfig(**models_dict['llm'])
                
        if 'preprocessing' in omega_config:
            preprocessing_dict = dict(omega_config.preprocessing)
            config.preprocessing = PreprocessingConfig(**preprocessing_dict)
            
        if 'training' in omega_config:
            config.training = TrainingConfig(**omega_config.training)
            
        if 'vector_db' in omega_config:
            config.vector_db = VectorDBConfig(**omega_config.vector_db)
            
        if 'api' in omega_config:
            config.api = APIConfig(**omega_config.api)
            
        if 'logging' in omega_config:
            config.logging = LoggingConfig(**omega_config.logging)
            
        if 'security' in omega_config:
            config.security = SecurityConfig(**omega_config.security)
            
        if 'evaluation' in omega_config:
            config.evaluation = EvaluationConfig(**omega_config.evaluation)
            
        if 'agents' in omega_config:
            config.agents = dict(omega_config.agents)
        
        return config
    
    def to_yaml(self, output_path: str):
        """
        Save configuration to YAML file.
        
        Args:
            output_path: Path to save YAML file
        """
        # Convert dataclass to dictionary
        config_dict = {
            'project': self.project,
            'data': {
                'mri_data_path': self.data.mri_data_path,
                'patient_records_path': self.data.patient_records_path,
                'knowledge_base_path': self.data.knowledge_base_path,
                'processed_data_path': self.data.processed_data_path,
            },
            'models': {
                'paligemma': {
                    'model_name': self.models.paligemma.model_name,
                    'checkpoint_path': self.models.paligemma.checkpoint_path,
                    'max_length': self.models.paligemma.max_length,
                    'image_size': self.models.paligemma.image_size,
                    'batch_size': self.models.paligemma.batch_size,
                    'learning_rate': self.models.paligemma.learning_rate,
                    'num_epochs': self.models.paligemma.num_epochs,
                },
                'llm': {
                    'model_name': self.models.llm.model_name,
                    'temperature': self.models.llm.temperature,
                    'max_tokens': self.models.llm.max_tokens,
                }
            },
            'preprocessing': {
                'image_size': self.preprocessing.image_size,
                'spacing': self.preprocessing.spacing,
                'intensity_range': self.preprocessing.intensity_range,
                'augmentation': self.preprocessing.augmentation,
            },
            'training': {
                'device': self.training.device,
                'mixed_precision': self.training.mixed_precision,
                'gradient_accumulation_steps': self.training.gradient_accumulation_steps,
                'warmup_steps': self.training.warmup_steps,
                'save_steps': self.training.save_steps,
                'eval_steps': self.training.eval_steps,
            },
            'vector_db': {
                'provider': self.vector_db.provider,
                'host': self.vector_db.host,
                'port': self.vector_db.port,
                'collection_name': self.vector_db.collection_name,
                'embedding_model': self.vector_db.embedding_model,
            },
            'api': {
                'host': self.api.host,
                'port': self.api.port,
                'debug': self.api.debug,
                'cors_origins': self.api.cors_origins,
            },
            'logging': {
                'level': self.logging.level,
                'format': self.logging.format,
                'file': self.logging.file,
            },
            'security': {
                'enable_encryption': self.security.enable_encryption,
                'hipaa_compliance': self.security.hipaa_compliance,
                'audit_logging': self.security.audit_logging,
            },
            'evaluation': {
                'segmentation_metrics': self.evaluation.segmentation_metrics,
                'nlp_metrics': self.evaluation.nlp_metrics,
            },
            'agents': self.agents,
        }
        
        with open(output_path, 'w') as f:
            yaml.dump(config_dict, f, default_flow_style=False, indent=2)
    
    def update_from_dict(self, update_dict: Dict[str, Any]):
        """
        Update configuration from dictionary.
        
        Args:
            update_dict: Dictionary with configuration updates
        """
        omega_config = OmegaConf.create(update_dict)
        
        # Update relevant fields
        for key, value in omega_config.items():
            if hasattr(self, key):
                if isinstance(getattr(self, key), dict):
                    getattr(self, key).update(value)
                else:
                    setattr(self, key, value)
    
    def get_model_config(self, model_name: str) -> Dict[str, Any]:
        """
        Get configuration for specific model.
        
        Args:
            model_name: Name of the model
            
        Returns:
            Model configuration dictionary
        """
        if model_name == "paligemma":
            return {
                'model_name': self.models.paligemma.model_name,
                'checkpoint_path': self.models.paligemma.checkpoint_path,
                'max_length': self.models.paligemma.max_length,
                'image_size': self.models.paligemma.image_size,
                'batch_size': self.models.paligemma.batch_size,
                'learning_rate': self.models.paligemma.learning_rate,
                'num_epochs': self.models.paligemma.num_epochs,
            }
        elif model_name == "llm":
            return {
                'model_name': self.models.llm.model_name,
                'temperature': self.models.llm.temperature,
                'max_tokens': self.models.llm.max_tokens,
            }
        else:
            raise ValueError(f"Unknown model name: {model_name}")


def load_config(config_path: Optional[str] = None) -> Config:
    """
    Load configuration from file or create default.
    
    Args:
        config_path: Path to configuration file (optional)
        
    Returns:
        Config object
    """
    if config_path is None:
        # Look for config in standard locations
        possible_paths = [
            "configs/config.yaml",
            "config.yaml",
            os.path.join(os.path.dirname(__file__), "..", "..", "..", "configs", "config.yaml")
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                config_path = path
                break
    
    if config_path and os.path.exists(config_path):
        return Config.from_yaml(config_path)
    else:
        # Return default configuration
        return Config()


def create_directories_from_config(config: Config):
    """
    Create necessary directories based on configuration.
    
    Args:
        config: Configuration object
    """
    directories = [
        config.data.mri_data_path,
        config.data.patient_records_path,
        config.data.knowledge_base_path,
        config.data.processed_data_path,
        config.models.paligemma.checkpoint_path,
        os.path.dirname(config.logging.file),
        "models/checkpoints",
        "logs",
        "outputs",
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
