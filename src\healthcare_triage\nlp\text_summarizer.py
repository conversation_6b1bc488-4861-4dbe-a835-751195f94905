"""
Text summarization for medical documents.
"""

import logging
from typing import Dict, List, Optional, Any

logger = logging.getLogger(__name__)


class TextSummarizer:
    """Text summarization for medical content."""
    
    def __init__(self, config):
        """Initialize text summarizer."""
        self.config = config
        
    def summarize_text(self, text: str, max_length: int = 200) -> str:
        """Summarize text content."""
        # Simple extractive summarization - in production use proper models
        sentences = text.split('. ')
        
        if len(sentences) <= 3:
            return text
        
        # Take first and last sentences as summary
        summary = '. '.join(sentences[:2] + sentences[-1:])
        
        if len(summary) > max_length:
            summary = summary[:max_length] + "..."
        
        return summary
