#!/usr/bin/env python3
"""
Project status checker for Healthcare Multimodal Triage Agent.

This script provides a comprehensive overview of the project status,
including implementation progress, dependencies, and next steps.
"""

import os
import sys
import subprocess
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple, Optional

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))


class ProjectStatusChecker:
    """Comprehensive project status checker."""
    
    def __init__(self):
        """Initialize the status checker."""
        self.project_root = Path(__file__).parent.parent
        self.status_report = {
            "timestamp": datetime.now().isoformat(),
            "project_structure": {},
            "dependencies": {},
            "implementation_status": {},
            "data_status": {},
            "configuration": {},
            "next_steps": []
        }
    
    def run_complete_status_check(self):
        """Run comprehensive status check."""
        print("🔍 HEALTHCARE MULTIMODAL TRIAGE AGENT - PROJECT STATUS")
        print("=" * 70)
        print(f"Status check performed at: {self.status_report['timestamp']}")
        print("=" * 70)
        
        # Check project structure
        self.check_project_structure()
        
        # Check dependencies
        self.check_dependencies()
        
        # Check implementation status
        self.check_implementation_status()
        
        # Check data status
        self.check_data_status()
        
        # Check configuration
        self.check_configuration()
        
        # Generate recommendations
        self.generate_recommendations()
        
        # Display summary
        self.display_summary()
        
        # Save detailed report
        self.save_detailed_report()
    
    def check_project_structure(self):
        """Check project directory structure."""
        print("\n📁 PROJECT STRUCTURE")
        print("-" * 30)
        
        required_structure = {
            "src/healthcare_triage": ["Core implementation"],
            "src/healthcare_triage/imaging": ["MRI processing modules"],
            "src/healthcare_triage/nlp": ["NLP and patient history processing"],
            "src/healthcare_triage/agents": ["LangChain agents"],
            "src/healthcare_triage/utils": ["Utility modules"],
            "configs": ["Configuration files"],
            "scripts": ["Automation scripts"],
            "data": ["Data directories"],
            "models": ["Model storage"],
            "tests": ["Test files"],
            "docs": ["Documentation"],
            "requirements.txt": ["Python dependencies"],
            "setup.py": ["Package setup"],
            "README.md": ["Project documentation"]
        }
        
        structure_status = {}
        
        for path, description in required_structure.items():
            full_path = self.project_root / path
            exists = full_path.exists()
            
            if exists:
                if full_path.is_dir():
                    file_count = len(list(full_path.rglob("*.py")))
                    status = f"✅ {path} ({file_count} Python files)"
                else:
                    status = f"✅ {path}"
            else:
                status = f"❌ {path} (missing)"
            
            print(status)
            structure_status[path] = {
                "exists": exists,
                "description": description[0],
                "status": "complete" if exists else "missing"
            }
        
        self.status_report["project_structure"] = structure_status
    
    def check_dependencies(self):
        """Check Python dependencies."""
        print("\n📦 DEPENDENCIES")
        print("-" * 30)
        
        critical_deps = [
            ("torch", "PyTorch for deep learning"),
            ("transformers", "Hugging Face Transformers"),
            ("monai", "Medical imaging framework"),
            ("langchain", "LLM orchestration"),
            ("nibabel", "Neuroimaging data I/O"),
            ("numpy", "Numerical computing"),
            ("pandas", "Data manipulation"),
            ("scikit-image", "Image processing"),
            ("matplotlib", "Plotting and visualization")
        ]
        
        optional_deps = [
            ("wandb", "Experiment tracking"),
            ("tensorboard", "Training monitoring"),
            ("qdrant-client", "Vector database"),
            ("openai", "OpenAI API"),
            ("anthropic", "Anthropic API")
        ]
        
        dep_status = {"critical": {}, "optional": {}}
        
        print("Critical Dependencies:")
        for dep_name, description in critical_deps:
            status = self._check_dependency(dep_name, description)
            print(f"  {status}")
            dep_status["critical"][dep_name] = status
        
        print("\nOptional Dependencies:")
        for dep_name, description in optional_deps:
            status = self._check_dependency(dep_name, description)
            print(f"  {status}")
            dep_status["optional"][dep_name] = status
        
        self.status_report["dependencies"] = dep_status
    
    def _check_dependency(self, dep_name: str, description: str) -> str:
        """Check if a dependency is installed."""
        try:
            module = __import__(dep_name)
            version = getattr(module, '__version__', 'Unknown')
            return f"✅ {dep_name} ({version}) - {description}"
        except ImportError:
            return f"❌ {dep_name} - {description} (Not installed)"
    
    def check_implementation_status(self):
        """Check implementation status of core modules."""
        print("\n🔧 IMPLEMENTATION STATUS")
        print("-" * 30)
        
        modules_to_check = {
            "src/healthcare_triage/imaging/mri_processor.py": "MRI Processing",
            "src/healthcare_triage/imaging/paligemma_segmenter.py": "PaliGemma Segmentation",
            "src/healthcare_triage/imaging/preprocessing.py": "Image Preprocessing",
            "src/healthcare_triage/imaging/augmentation.py": "Data Augmentation",
            "src/healthcare_triage/imaging/postprocessing.py": "Post-processing",
            "src/healthcare_triage/nlp/patient_history_processor.py": "Patient History Processing",
            "src/healthcare_triage/agents/triage_agent.py": "Triage Agent",
            "src/healthcare_triage/utils/config.py": "Configuration Management",
            "src/healthcare_triage/utils/logging_utils.py": "Logging Utilities",
            "scripts/setup_environment.py": "Environment Setup",
            "scripts/download_sample_data.py": "Data Download",
            "scripts/train_model.py": "Model Training",
            "scripts/run_inference.py": "Inference Pipeline",
            "scripts/demo.py": "Complete Demo"
        }
        
        impl_status = {}
        
        for file_path, description in modules_to_check.items():
            full_path = self.project_root / file_path
            
            if full_path.exists():
                # Count lines of code
                try:
                    with open(full_path, 'r', encoding='utf-8') as f:
                        lines = len([line for line in f if line.strip() and not line.strip().startswith('#')])
                    status = f"✅ {description} ({lines} LOC)"
                    impl_status[file_path] = {"status": "implemented", "lines": lines}
                except Exception:
                    status = f"✅ {description} (exists)"
                    impl_status[file_path] = {"status": "implemented", "lines": 0}
            else:
                status = f"❌ {description} (not implemented)"
                impl_status[file_path] = {"status": "missing", "lines": 0}
            
            print(status)
        
        self.status_report["implementation_status"] = impl_status
    
    def check_data_status(self):
        """Check data directory status."""
        print("\n📊 DATA STATUS")
        print("-" * 30)
        
        data_dirs = {
            "data/mri/train": "Training MRI data",
            "data/mri/val": "Validation MRI data", 
            "data/mri/test": "Test MRI data",
            "data/patient_records": "Patient history data",
            "data/knowledge_base": "Medical knowledge base",
            "models/paligemma": "PaliGemma models",
            "models/checkpoints": "Training checkpoints"
        }
        
        data_status = {}
        
        for dir_path, description in data_dirs.items():
            full_path = self.project_root / dir_path
            
            if full_path.exists():
                file_count = len(list(full_path.rglob("*")))
                status = f"✅ {description} ({file_count} files)"
                data_status[dir_path] = {"exists": True, "file_count": file_count}
            else:
                status = f"❌ {description} (not found)"
                data_status[dir_path] = {"exists": False, "file_count": 0}
            
            print(status)
        
        self.status_report["data_status"] = data_status
    
    def check_configuration(self):
        """Check configuration status."""
        print("\n⚙️  CONFIGURATION")
        print("-" * 30)
        
        config_files = {
            "configs/config.yaml": "Main configuration",
            ".env": "Environment variables",
            "requirements.txt": "Python dependencies",
            "setup.py": "Package setup"
        }
        
        config_status = {}
        
        for file_path, description in config_files.items():
            full_path = self.project_root / file_path
            
            if full_path.exists():
                status = f"✅ {description}"
                config_status[file_path] = {"exists": True}
            else:
                status = f"❌ {description} (missing)"
                config_status[file_path] = {"exists": False}
            
            print(status)
        
        # Check API keys
        api_keys = ["OPENAI_API_KEY", "ANTHROPIC_API_KEY"]
        print("\nAPI Keys:")
        for key in api_keys:
            if os.getenv(key):
                print(f"  ✅ {key} (configured)")
                config_status[key] = {"configured": True}
            else:
                print(f"  ❌ {key} (not configured)")
                config_status[key] = {"configured": False}
        
        self.status_report["configuration"] = config_status
    
    def generate_recommendations(self):
        """Generate next steps and recommendations."""
        recommendations = []
        
        # Check critical missing components
        missing_deps = [
            dep for dep, status in self.status_report["dependencies"]["critical"].items()
            if "❌" in status
        ]
        
        if missing_deps:
            recommendations.append({
                "priority": "HIGH",
                "category": "Dependencies",
                "action": f"Install missing critical dependencies: {', '.join(missing_deps)}",
                "command": "pip install -r requirements.txt"
            })
        
        # Check data availability
        if not self.status_report["data_status"]["data/mri/train"]["exists"]:
            recommendations.append({
                "priority": "HIGH", 
                "category": "Data",
                "action": "Set up training data",
                "command": "python scripts/download_sample_data.py --all"
            })
        
        # Check API configuration
        if not self.status_report["configuration"].get("OPENAI_API_KEY", {}).get("configured"):
            recommendations.append({
                "priority": "MEDIUM",
                "category": "Configuration",
                "action": "Configure API keys for LLM services",
                "command": "Edit .env file with your API keys"
            })
        
        # Training recommendations
        if self.status_report["data_status"]["data/mri/train"]["exists"]:
            recommendations.append({
                "priority": "MEDIUM",
                "category": "Training",
                "action": "Start model training",
                "command": "python scripts/train_model.py"
            })
        
        # Testing recommendations
        recommendations.append({
            "priority": "LOW",
            "category": "Testing",
            "action": "Run comprehensive demo",
            "command": "python scripts/demo.py"
        })
        
        self.status_report["next_steps"] = recommendations
    
    def display_summary(self):
        """Display project status summary."""
        print("\n📋 PROJECT SUMMARY")
        print("=" * 70)
        
        # Calculate completion percentages
        structure_complete = sum(1 for s in self.status_report["project_structure"].values() if s["exists"])
        structure_total = len(self.status_report["project_structure"])
        structure_pct = (structure_complete / structure_total) * 100
        
        impl_complete = sum(1 for s in self.status_report["implementation_status"].values() if s["status"] == "implemented")
        impl_total = len(self.status_report["implementation_status"])
        impl_pct = (impl_complete / impl_total) * 100
        
        print(f"📁 Project Structure: {structure_pct:.1f}% complete ({structure_complete}/{structure_total})")
        print(f"🔧 Implementation: {impl_pct:.1f}% complete ({impl_complete}/{impl_total})")
        
        # Critical dependencies
        critical_deps = self.status_report["dependencies"]["critical"]
        critical_ok = sum(1 for status in critical_deps.values() if "✅" in status)
        critical_total = len(critical_deps)
        print(f"📦 Critical Dependencies: {critical_ok}/{critical_total} installed")
        
        # Data availability
        data_dirs = self.status_report["data_status"]
        data_ok = sum(1 for status in data_dirs.values() if status["exists"])
        data_total = len(data_dirs)
        print(f"📊 Data Directories: {data_ok}/{data_total} available")
        
        # Overall status
        overall_score = (structure_pct + impl_pct) / 2
        if overall_score >= 90:
            status_emoji = "🟢"
            status_text = "READY FOR PRODUCTION"
        elif overall_score >= 70:
            status_emoji = "🟡"
            status_text = "READY FOR TESTING"
        elif overall_score >= 50:
            status_emoji = "🟠"
            status_text = "IN DEVELOPMENT"
        else:
            status_emoji = "🔴"
            status_text = "SETUP REQUIRED"
        
        print(f"\n{status_emoji} Overall Status: {status_text} ({overall_score:.1f}%)")
        
        # Next steps
        print(f"\n🎯 IMMEDIATE NEXT STEPS")
        print("-" * 30)
        high_priority = [r for r in self.status_report["next_steps"] if r["priority"] == "HIGH"]
        
        if high_priority:
            for i, rec in enumerate(high_priority[:3], 1):
                print(f"{i}. {rec['action']}")
                print(f"   Command: {rec['command']}")
        else:
            print("✅ All critical setup steps completed!")
            print("Ready to run: python scripts/demo.py")
    
    def save_detailed_report(self):
        """Save detailed status report."""
        import json
        
        output_dir = self.project_root / "outputs" / "status"
        output_dir.mkdir(parents=True, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_path = output_dir / f"project_status_{timestamp}.json"
        
        with open(report_path, 'w') as f:
            json.dump(self.status_report, f, indent=2, default=str)
        
        print(f"\n📄 Detailed report saved to: {report_path}")


def main():
    """Run project status check."""
    try:
        checker = ProjectStatusChecker()
        checker.run_complete_status_check()
    except Exception as e:
        print(f"Status check failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
