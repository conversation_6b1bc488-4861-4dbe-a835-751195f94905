2025-08-09 04:19:35,380 - healthcare_triage - INFO - Logging system initialized
2025-08-09 04:19:35,383 - healthcare_triage - INFO - Healthcare Multimodal Triage Agent - Logging System Initialized
2025-08-09 04:19:35,383 - healthcare_triage - INFO - Python version: 3.12.4 | packaged by Anaconda, Inc. | (main, Jun 18 2024, 15:03:56) [MSC v.1929 64 bit (AMD64)]
2025-08-09 04:19:35,384 - healthcare_triage - INFO - Working directory: F:\Cutie
2025-08-09 04:19:35,384 - healthcare_triage - INFO - HIPAA compliance mode enabled - <PERSON>t logging active
2025-08-09 04:19:35,385 - healthcare_triage - INFO - Downloading BraTS 2020 dataset from Kaggle...
2025-08-09 04:34:45,934 - healthcare_triage - ERROR - Failed to download dataset: [<PERSON>rrno 28] No space left on device
2025-08-09 04:34:45,936 - healthcare_triage - ERROR - Dataset setup failed: [<PERSON><PERSON><PERSON> 28] No space left on device
2025-08-09 04:37:57,698 - healthcare_triage - INFO - Logging system initialized
2025-08-09 04:37:57,701 - healthcare_triage - INFO - Healthcare Multimodal Triage Agent - Logging System Initialized
2025-08-09 04:37:57,701 - healthcare_triage - INFO - Python version: 3.12.4 | packaged by Anaconda, Inc. | (main, Jun 18 2024, 15:03:56) [MSC v.1929 64 bit (AMD64)]
2025-08-09 04:37:57,702 - healthcare_triage - INFO - Working directory: F:\Cutie
2025-08-09 04:37:57,702 - healthcare_triage - INFO - HIPAA compliance mode enabled - Audit logging active
2025-08-09 04:37:57,703 - healthcare_triage - INFO - Using device: cpu
2025-08-09 04:37:57,749 - healthcare_triage - INFO - Simple trainer initialized
2025-08-09 04:37:57,751 - healthcare_triage - INFO - Running complete demonstration...
2025-08-09 04:37:57,751 - healthcare_triage - INFO - Testing patient history processing...
2025-08-09 04:37:57,752 - healthcare_triage.nlp.patient_history_processor - INFO - Loaded ehr data for patient patient_001
2025-08-09 04:37:57,752 - healthcare_triage.nlp.patient_history_processor - INFO - Loaded documents data for patient patient_001
2025-08-09 04:37:57,753 - healthcare_triage.nlp.patient_history_processor - INFO - Loaded lab_reports data for patient patient_001
2025-08-09 04:37:57,754 - healthcare_triage.nlp.patient_history_processor - INFO - Loaded imaging_reports data for patient patient_001
2025-08-09 04:37:57,754 - healthcare_triage.nlp.patient_history_processor - INFO - Processed patient history for patient_001
2025-08-09 04:37:57,755 - healthcare_triage - INFO - Patient processing test completed
2025-08-09 04:37:57,756 - healthcare_triage - INFO - Starting training for 3 epochs...
2025-08-09 04:37:57,756 - healthcare_triage - INFO - Preparing datasets...
2025-08-09 04:37:57,760 - healthcare_triage - INFO - Training samples: 10
2025-08-09 04:37:57,761 - healthcare_triage - INFO - Validation samples: 5
2025-08-09 04:37:57,761 - healthcare_triage - INFO - Starting epoch 1/3
2025-08-09 04:37:58,514 - healthcare_triage - INFO - Epoch 1, Batch 0/10, Loss: 0.7096
2025-08-09 04:38:01,597 - healthcare_triage - INFO - Epoch 1, Batch 5/10, Loss: 0.6883
2025-08-09 04:38:03,887 - healthcare_triage - INFO - Epoch 1 - Average training loss: 0.6894
2025-08-09 04:38:04,597 - healthcare_triage - INFO - Epoch 1 - Average validation loss: 0.6600
2025-08-09 04:38:04,597 - healthcare_triage - INFO - Starting epoch 2/3
2025-08-09 04:38:05,150 - healthcare_triage - INFO - Epoch 2, Batch 0/10, Loss: 0.6597
2025-08-09 04:38:07,945 - healthcare_triage - INFO - Epoch 2, Batch 5/10, Loss: 0.6026
2025-08-09 04:38:10,928 - healthcare_triage - INFO - Epoch 2 - Average training loss: 0.6047
2025-08-09 04:38:11,753 - healthcare_triage - INFO - Epoch 2 - Average validation loss: 0.5112
2025-08-09 04:38:11,764 - healthcare_triage - INFO - Checkpoint saved: models/checkpoints/simple_model_epoch_2.pth
2025-08-09 04:38:11,765 - healthcare_triage - INFO - Starting epoch 3/3
2025-08-09 04:38:12,405 - healthcare_triage - INFO - Epoch 3, Batch 0/10, Loss: 0.5107
2025-08-09 04:38:15,802 - healthcare_triage - INFO - Epoch 3, Batch 5/10, Loss: 0.3981
2025-08-09 04:38:18,499 - healthcare_triage - INFO - Epoch 3 - Average training loss: 0.4086
2025-08-09 04:38:19,341 - healthcare_triage - INFO - Epoch 3 - Average validation loss: 0.2914
2025-08-09 04:38:19,342 - healthcare_triage - INFO - Training completed!
2025-08-09 04:38:19,343 - healthcare_triage - INFO - Testing inference...
2025-08-09 04:38:19,532 - healthcare_triage - INFO - Inference completed - Output shape: torch.Size([1, 1, 64, 64, 64])
2025-08-09 04:38:19,631 - healthcare_triage - INFO - Sample prediction saved to outputs\inference
2025-08-09 04:38:19,631 - healthcare_triage - INFO - Complete demonstration finished!
2025-08-09 04:42:19,269 - healthcare_triage - INFO - Logging system initialized
2025-08-09 04:42:19,272 - healthcare_triage - INFO - Healthcare Multimodal Triage Agent - Logging System Initialized
2025-08-09 04:42:19,273 - healthcare_triage - INFO - Python version: 3.12.4 | packaged by Anaconda, Inc. | (main, Jun 18 2024, 15:03:56) [MSC v.1929 64 bit (AMD64)]
2025-08-09 04:42:19,273 - healthcare_triage - INFO - Working directory: F:\Cutie
2025-08-09 04:42:19,274 - healthcare_triage - INFO - HIPAA compliance mode enabled - Audit logging active
2025-08-09 04:42:19,274 - healthcare_triage - INFO - Using device: cpu
2025-08-09 04:42:19,307 - healthcare_triage - INFO - Loading latest checkpoint: models\checkpoints\simple_model_epoch_2.pth
2025-08-09 04:42:19,311 - root - ERROR - Inference failed: No module named 'scripts.train_simple'
2025-08-09 04:43:42,412 - healthcare_triage - INFO - Logging system initialized
2025-08-09 04:43:42,414 - healthcare_triage - INFO - Healthcare Multimodal Triage Agent - Logging System Initialized
2025-08-09 04:43:42,415 - healthcare_triage - INFO - Python version: 3.12.4 | packaged by Anaconda, Inc. | (main, Jun 18 2024, 15:03:56) [MSC v.1929 64 bit (AMD64)]
2025-08-09 04:43:42,416 - healthcare_triage - INFO - Working directory: F:\Cutie
2025-08-09 04:43:42,416 - healthcare_triage - INFO - HIPAA compliance mode enabled - Audit logging active
2025-08-09 04:43:42,417 - healthcare_triage - INFO - Using device: cpu
2025-08-09 04:43:42,446 - healthcare_triage - INFO - Loading latest checkpoint: models\checkpoints\simple_model_epoch_2.pth
2025-08-09 04:43:42,524 - healthcare_triage - INFO - Model loaded from models\checkpoints\simple_model_epoch_2.pth
2025-08-09 04:43:42,525 - healthcare_triage - INFO - Healthcare Triage Inference system initialized
2025-08-09 04:43:42,526 - healthcare_triage - INFO - Running inference for patient patient_test_000.nii
2025-08-09 04:43:42,527 - healthcare_triage - WARNING - No patient record found for patient_test_000.nii
2025-08-09 04:43:42,527 - healthcare_triage.nlp.patient_history_processor - INFO - Loaded ehr data for patient patient_test_000.nii
2025-08-09 04:43:42,528 - healthcare_triage.nlp.patient_history_processor - INFO - Loaded documents data for patient patient_test_000.nii
2025-08-09 04:43:42,529 - healthcare_triage.nlp.patient_history_processor - INFO - Loaded lab_reports data for patient patient_test_000.nii
2025-08-09 04:43:42,529 - healthcare_triage.nlp.patient_history_processor - INFO - Loaded imaging_reports data for patient patient_test_000.nii
2025-08-09 04:43:42,530 - healthcare_triage.nlp.patient_history_processor - INFO - Processed patient history for patient_test_000.nii
2025-08-09 04:43:42,531 - healthcare_triage - INFO - Loaded patient history for patient_test_000.nii
2025-08-09 04:43:42,532 - healthcare_triage - ERROR - Failed to process patient patient_test_000.nii: MRI image not found for patient patient_test_000.nii
2025-08-09 04:43:42,533 - healthcare_triage - INFO - Running inference for patient patient_test_001.nii
2025-08-09 04:43:42,534 - healthcare_triage - WARNING - No patient record found for patient_test_001.nii
2025-08-09 04:43:42,535 - healthcare_triage.nlp.patient_history_processor - INFO - Loaded ehr data for patient patient_test_001.nii
2025-08-09 04:43:42,535 - healthcare_triage.nlp.patient_history_processor - INFO - Loaded documents data for patient patient_test_001.nii
2025-08-09 04:43:42,536 - healthcare_triage.nlp.patient_history_processor - INFO - Loaded lab_reports data for patient patient_test_001.nii
2025-08-09 04:43:42,537 - healthcare_triage.nlp.patient_history_processor - INFO - Loaded imaging_reports data for patient patient_test_001.nii
2025-08-09 04:43:42,537 - healthcare_triage.nlp.patient_history_processor - INFO - Processed patient history for patient_test_001.nii
2025-08-09 04:43:42,538 - healthcare_triage - INFO - Loaded patient history for patient_test_001.nii
2025-08-09 04:43:42,539 - healthcare_triage - ERROR - Failed to process patient patient_test_001.nii: MRI image not found for patient patient_test_001.nii
2025-08-09 04:43:42,539 - healthcare_triage - INFO - Running inference for patient patient_test_002.nii
2025-08-09 04:43:42,541 - healthcare_triage - WARNING - No patient record found for patient_test_002.nii
2025-08-09 04:43:42,542 - healthcare_triage.nlp.patient_history_processor - INFO - Loaded ehr data for patient patient_test_002.nii
2025-08-09 04:43:42,542 - healthcare_triage.nlp.patient_history_processor - INFO - Loaded documents data for patient patient_test_002.nii
2025-08-09 04:43:42,543 - healthcare_triage.nlp.patient_history_processor - INFO - Loaded lab_reports data for patient patient_test_002.nii
2025-08-09 04:43:42,544 - healthcare_triage.nlp.patient_history_processor - INFO - Loaded imaging_reports data for patient patient_test_002.nii
2025-08-09 04:43:42,545 - healthcare_triage.nlp.patient_history_processor - INFO - Processed patient history for patient_test_002.nii
2025-08-09 04:43:42,546 - healthcare_triage - INFO - Loaded patient history for patient_test_002.nii
2025-08-09 04:43:42,546 - healthcare_triage - ERROR - Failed to process patient patient_test_002.nii: MRI image not found for patient patient_test_002.nii
2025-08-09 04:44:50,240 - healthcare_triage - INFO - Logging system initialized
2025-08-09 04:44:50,243 - healthcare_triage - INFO - Healthcare Multimodal Triage Agent - Logging System Initialized
2025-08-09 04:44:50,244 - healthcare_triage - INFO - Python version: 3.12.4 | packaged by Anaconda, Inc. | (main, Jun 18 2024, 15:03:56) [MSC v.1929 64 bit (AMD64)]
2025-08-09 04:44:50,245 - healthcare_triage - INFO - Working directory: F:\Cutie
2025-08-09 04:44:50,245 - healthcare_triage - INFO - HIPAA compliance mode enabled - Audit logging active
2025-08-09 04:44:50,246 - healthcare_triage - INFO - Using device: cpu
2025-08-09 04:44:50,281 - healthcare_triage - INFO - Loading latest checkpoint: models\checkpoints\simple_model_epoch_2.pth
2025-08-09 04:44:50,323 - healthcare_triage - INFO - Model loaded from models\checkpoints\simple_model_epoch_2.pth
2025-08-09 04:44:50,324 - healthcare_triage - INFO - Healthcare Triage Inference system initialized
2025-08-09 04:44:50,325 - healthcare_triage - INFO - Running inference for patient patient_test_000
2025-08-09 04:44:50,327 - healthcare_triage - INFO - Loaded patient record for patient_test_000
2025-08-09 04:44:50,328 - healthcare_triage.nlp.patient_history_processor - INFO - Loaded ehr data for patient patient_test_000
2025-08-09 04:44:50,328 - healthcare_triage.nlp.patient_history_processor - INFO - Loaded documents data for patient patient_test_000
2025-08-09 04:44:50,330 - healthcare_triage.nlp.patient_history_processor - INFO - Loaded lab_reports data for patient patient_test_000
2025-08-09 04:44:50,331 - healthcare_triage.nlp.patient_history_processor - INFO - Loaded imaging_reports data for patient patient_test_000
2025-08-09 04:44:50,332 - healthcare_triage.nlp.patient_history_processor - INFO - Processed patient history for patient_test_000
2025-08-09 04:44:50,333 - healthcare_triage - INFO - Loaded patient history for patient_test_000
2025-08-09 04:44:50,619 - healthcare_triage - INFO - Inference completed for patient_test_000
2025-08-09 04:44:50,619 - healthcare_triage - INFO - Triage level: ROUTINE
2025-08-09 04:44:50,620 - healthcare_triage - INFO - Recommendation: Routine follow-up with primary care physician
2025-08-09 04:44:50,621 - healthcare_triage - INFO - Running inference for patient patient_test_001
2025-08-09 04:44:50,623 - healthcare_triage - INFO - Loaded patient record for patient_test_001
2025-08-09 04:44:50,624 - healthcare_triage.nlp.patient_history_processor - INFO - Loaded ehr data for patient patient_test_001
2025-08-09 04:44:50,625 - healthcare_triage.nlp.patient_history_processor - INFO - Loaded documents data for patient patient_test_001
2025-08-09 04:44:50,626 - healthcare_triage.nlp.patient_history_processor - INFO - Loaded lab_reports data for patient patient_test_001
2025-08-09 04:44:50,626 - healthcare_triage.nlp.patient_history_processor - INFO - Loaded imaging_reports data for patient patient_test_001
2025-08-09 04:44:50,627 - healthcare_triage.nlp.patient_history_processor - INFO - Processed patient history for patient_test_001
2025-08-09 04:44:50,627 - healthcare_triage - INFO - Loaded patient history for patient_test_001
2025-08-09 04:44:50,892 - healthcare_triage - INFO - Inference completed for patient_test_001
2025-08-09 04:44:50,893 - healthcare_triage - INFO - Triage level: LOW
2025-08-09 04:44:50,893 - healthcare_triage - INFO - Recommendation: No immediate action required, routine monitoring
2025-08-09 04:44:50,894 - healthcare_triage - INFO - Running inference for patient patient_test_002
2025-08-09 04:44:50,895 - healthcare_triage - INFO - Loaded patient record for patient_test_002
2025-08-09 04:44:50,896 - healthcare_triage.nlp.patient_history_processor - INFO - Loaded ehr data for patient patient_test_002
2025-08-09 04:44:50,897 - healthcare_triage.nlp.patient_history_processor - INFO - Loaded documents data for patient patient_test_002
2025-08-09 04:44:50,899 - healthcare_triage.nlp.patient_history_processor - INFO - Loaded lab_reports data for patient patient_test_002
2025-08-09 04:44:50,900 - healthcare_triage.nlp.patient_history_processor - INFO - Loaded imaging_reports data for patient patient_test_002
2025-08-09 04:44:50,900 - healthcare_triage.nlp.patient_history_processor - INFO - Processed patient history for patient_test_002
2025-08-09 04:44:50,902 - healthcare_triage - INFO - Loaded patient history for patient_test_002
2025-08-09 04:44:51,155 - healthcare_triage - INFO - Inference completed for patient_test_002
2025-08-09 04:44:51,155 - healthcare_triage - INFO - Triage level: ROUTINE
2025-08-09 04:44:51,156 - healthcare_triage - INFO - Recommendation: Routine follow-up with primary care physician
