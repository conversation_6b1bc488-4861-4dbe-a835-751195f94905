"""
Information extraction from medical texts.
"""

import logging
import re
from typing import Dict, List, Optional, Any

logger = logging.getLogger(__name__)


class InformationExtractor:
    """Extract medical information from text."""
    
    def __init__(self, config):
        """Initialize information extractor."""
        self.config = config
        
    def extract_from_text(self, text: str) -> Dict[str, List[str]]:
        """Extract medical information from text."""
        extracted = {
            "diagnoses": [],
            "medications": [],
            "allergies": [],
            "procedures": [],
            "symptoms": []
        }
        
        # Simple pattern-based extraction
        text_lower = text.lower()
        
        # Extract diagnoses
        diagnosis_patterns = [
            r"diagnosis[:\s]+([^.]+)",
            r"diagnosed with ([^.]+)",
            r"condition[:\s]+([^.]+)"
        ]
        
        for pattern in diagnosis_patterns:
            matches = re.findall(pattern, text_lower)
            extracted["diagnoses"].extend([m.strip() for m in matches])
        
        # Extract medications
        med_patterns = [
            r"medication[s]?[:\s]+([^.]+)",
            r"prescribed ([^.]+)",
            r"taking ([^.]+)"
        ]
        
        for pattern in med_patterns:
            matches = re.findall(pattern, text_lower)
            extracted["medications"].extend([m.strip() for m in matches])
        
        # Extract allergies
        allergy_patterns = [
            r"allergic to ([^.]+)",
            r"allerg[y|ies][:\s]+([^.]+)"
        ]
        
        for pattern in allergy_patterns:
            matches = re.findall(pattern, text_lower)
            extracted["allergies"].extend([m.strip() for m in matches])
        
        return extracted
