#!/usr/bin/env python3
"""
Demo script for Healthcare Multimodal Triage Agent.

This script demonstrates the complete workflow with synthetic data:
1. Environment setup and validation
2. Data preparation and download
3. Model training (abbreviated)
4. Inference pipeline demonstration
5. Results visualization and reporting
"""

import os
import sys
import time
import logging
from pathlib import Path
from datetime import datetime

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from healthcare_triage.utils.config import load_config
from healthcare_triage.utils.logging_utils import setup_comprehensive_logging


class HealthcareTriageDemo:
    """Complete demonstration of the Healthcare Triage Agent."""
    
    def __init__(self):
        """Initialize the demo."""
        self.config = load_config()
        self.logger = setup_comprehensive_logging(self.config)
        self.demo_start_time = datetime.now()
        
        print("\n" + "="*80)
        print("HEALTHCARE MULTIMODAL TRIAGE AGENT - COMPLETE DEMO")
        print("="*80)
        print(f"Demo started at: {self.demo_start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print("="*80)
    
    def run_complete_demo(self):
        """Run the complete demonstration workflow."""
        try:
            # Step 1: Environment Setup
            self.demo_step_1_environment_setup()
            
            # Step 2: Data Preparation
            self.demo_step_2_data_preparation()
            
            # Step 3: Model Training Demo
            self.demo_step_3_model_training()
            
            # Step 4: Inference Pipeline Demo
            self.demo_step_4_inference_pipeline()
            
            # Step 5: Results and Reporting
            self.demo_step_5_results_reporting()
            
            # Demo Summary
            self.demo_summary()
            
        except Exception as e:
            self.logger.error(f"Demo failed: {e}")
            print(f"\n❌ Demo failed: {e}")
            sys.exit(1)
    
    def demo_step_1_environment_setup(self):
        """Demonstrate environment setup and validation."""
        print("\n🔧 STEP 1: ENVIRONMENT SETUP AND VALIDATION")
        print("-" * 50)
        
        # Check Python version
        print(f"✓ Python version: {sys.version.split()[0]}")
        
        # Check key dependencies
        dependencies = [
            ("torch", "PyTorch"),
            ("transformers", "Transformers"),
            ("monai", "MONAI"),
            ("langchain", "LangChain"),
            ("nibabel", "NiBabel"),
            ("numpy", "NumPy"),
            ("pandas", "Pandas")
        ]
        
        for module_name, display_name in dependencies:
            try:
                module = __import__(module_name)
                version = getattr(module, '__version__', 'Unknown')
                print(f"✓ {display_name}: {version}")
            except ImportError:
                print(f"❌ {display_name}: Not installed")
        
        # Check CUDA availability
        try:
            import torch
            if torch.cuda.is_available():
                print(f"✓ CUDA: Available (GPU: {torch.cuda.get_device_name(0)})")
            else:
                print("⚠️  CUDA: Not available (CPU mode)")
        except:
            print("❌ CUDA: Cannot check")
        
        # Validate directory structure
        required_dirs = [
            "src/healthcare_triage",
            "configs",
            "data",
            "models",
            "scripts"
        ]
        
        print("\nDirectory structure:")
        for directory in required_dirs:
            if Path(directory).exists():
                print(f"✓ {directory}")
            else:
                print(f"❌ {directory}")
        
        print("✅ Environment setup validation completed")
        time.sleep(2)
    
    def demo_step_2_data_preparation(self):
        """Demonstrate data preparation and synthetic data generation."""
        print("\n📊 STEP 2: DATA PREPARATION")
        print("-" * 50)
        
        # Run data download script
        print("Preparing synthetic datasets...")
        
        try:
            # Import and run data downloader
            sys.path.append(str(Path(__file__).parent))
            from download_sample_data import DatasetDownloader
            
            downloader = DatasetDownloader(self.config)
            
            # Create synthetic MRI data
            print("Creating synthetic MRI data...")
            downloader.download_dataset("sample_mri")
            print("✓ Synthetic MRI data created")
            
            # Create medical knowledge base
            print("Creating medical knowledge base...")
            downloader.download_dataset("medical_knowledge")
            print("✓ Medical knowledge base created")
            
            # Verify data structure
            if downloader.verify_data_structure():
                print("✅ Data preparation completed successfully")
            else:
                print("⚠️  Data structure verification had warnings")
                
        except Exception as e:
            print(f"⚠️  Data preparation encountered issues: {e}")
            print("Continuing with available data...")
        
        time.sleep(2)
    
    def demo_step_3_model_training(self):
        """Demonstrate model training workflow (abbreviated for demo)."""
        print("\n🤖 STEP 3: MODEL TRAINING DEMONSTRATION")
        print("-" * 50)
        
        print("Note: This is an abbreviated training demo for illustration purposes.")
        print("Full training would require substantial computational resources and time.")
        
        # Initialize components
        try:
            from healthcare_triage.imaging import MRIProcessor, PaliGemmaSegmenter
            
            print("\nInitializing training components...")
            mri_processor = MRIProcessor(self.config)
            print("✓ MRI Processor initialized")
            
            # Note: PaliGemma initialization might fail without proper model access
            try:
                segmenter = PaliGemmaSegmenter(self.config)
                print("✓ PaliGemma Segmenter initialized")
                model_available = True
            except Exception as e:
                print(f"⚠️  PaliGemma Segmenter: {e}")
                print("   (This is expected in demo mode without model access)")
                model_available = False
            
            # Demonstrate data loading
            print("\nDemonstrating data loading...")
            try:
                train_data_path = Path(self.config.data.mri_data_path) / "train"
                if train_data_path.exists():
                    train_dataset = mri_processor.load_dataset(train_data_path, "train")
                    print(f"✓ Training dataset loaded: {len(train_dataset)} samples")
                else:
                    print("⚠️  Training data not found")
            except Exception as e:
                print(f"⚠️  Data loading demo: {e}")
            
            if model_available:
                print("\n🎯 Training Process (Simulated):")
                print("   1. Data preprocessing and augmentation")
                print("   2. Model fine-tuning with medical data")
                print("   3. Validation and checkpoint saving")
                print("   4. Performance evaluation")
                print("✓ Training workflow demonstrated")
            else:
                print("\n⚠️  Actual training skipped due to model access limitations")
                
        except Exception as e:
            print(f"⚠️  Training demo encountered issues: {e}")
        
        print("✅ Model training demonstration completed")
        time.sleep(2)
    
    def demo_step_4_inference_pipeline(self):
        """Demonstrate the complete inference pipeline."""
        print("\n🔍 STEP 4: INFERENCE PIPELINE DEMONSTRATION")
        print("-" * 50)
        
        try:
            # Initialize pipeline components
            from healthcare_triage.imaging import MRIProcessor
            from healthcare_triage.nlp import PatientHistoryProcessor
            from healthcare_triage.agents import TriageAgent
            
            print("Initializing inference components...")
            mri_processor = MRIProcessor(self.config)
            patient_processor = PatientHistoryProcessor(self.config)
            
            # Note: Triage agent requires API keys
            try:
                triage_agent = TriageAgent(self.config)
                print("✓ Triage Agent initialized")
                agent_available = True
            except Exception as e:
                print(f"⚠️  Triage Agent: {e}")
                print("   (API keys may be required for full functionality)")
                agent_available = False
            
            # Demonstrate MRI processing
            print("\nDemonstrating MRI processing...")
            test_image_path = Path("data/mri/test/images")
            if test_image_path.exists():
                test_images = list(test_image_path.glob("*.nii.gz"))
                if test_images:
                    sample_image = test_images[0]
                    print(f"Processing sample image: {sample_image.name}")
                    
                    try:
                        processed_image = mri_processor.preprocess_single_image(str(sample_image))
                        print(f"✓ Image processed: shape {processed_image.shape}")
                    except Exception as e:
                        print(f"⚠️  Image processing: {e}")
                else:
                    print("⚠️  No test images found")
            else:
                print("⚠️  Test image directory not found")
            
            # Demonstrate patient history processing
            print("\nDemonstrating patient history processing...")
            try:
                sample_patient_data = patient_processor.load_patient_data("patient_001")
                processed_data = patient_processor.process_patient_history(sample_patient_data)
                print("✓ Patient history processed")
                print(f"   Data sources: {list(sample_patient_data.get('raw_data', {}).keys())}")
            except Exception as e:
                print(f"⚠️  Patient history processing: {e}")
            
            # Demonstrate triage assessment (if agent available)
            if agent_available:
                print("\nDemonstrating triage assessment...")
                try:
                    # Create mock data for demo
                    mock_patient_data = {
                        "patient_id": "demo_patient",
                        "summary": {
                            "primary_diagnoses": ["Headaches", "Cognitive changes"],
                            "current_medications": ["Ibuprofen"],
                            "known_allergies": ["None known"],
                            "risk_factors": ["Age > 65"]
                        }
                    }
                    
                    mock_imaging_results = {
                        "generated_text": "Brain MRI shows suspicious lesion in frontal lobe",
                        "segmentation_info": {
                            "has_tumor": True,
                            "tumor_type": "suspected glioma",
                            "location": "frontal"
                        }
                    }
                    
                    # This would normally call the actual agent
                    print("✓ Triage assessment framework ready")
                    print("   (Full assessment requires API access)")
                    
                except Exception as e:
                    print(f"⚠️  Triage assessment demo: {e}")
            
        except Exception as e:
            print(f"⚠️  Inference pipeline demo: {e}")
        
        print("✅ Inference pipeline demonstration completed")
        time.sleep(2)
    
    def demo_step_5_results_reporting(self):
        """Demonstrate results reporting and visualization."""
        print("\n📋 STEP 5: RESULTS AND REPORTING")
        print("-" * 50)
        
        # Create sample triage report
        sample_report = {
            "patient_id": "demo_patient_001",
            "assessment_timestamp": datetime.now().isoformat(),
            "executive_summary": {
                "priority_level": "Urgent",
                "confidence_score": 0.85,
                "key_findings": [
                    "Suspicious brain lesion detected on MRI",
                    "Patient reports progressive headaches",
                    "Age-related risk factors present"
                ],
                "immediate_actions": [
                    "Neurology consultation within 24 hours",
                    "Additional imaging studies",
                    "Symptom monitoring"
                ]
            },
            "imaging_analysis": {
                "modality": "MRI Brain",
                "ai_interpretation": "Heterogeneous lesion in right frontal lobe with surrounding edema",
                "technical_quality": "Good"
            },
            "triage_assessment": {
                "priority_level": "Urgent",
                "reasoning": "Combination of imaging findings and clinical presentation suggests need for prompt evaluation",
                "recommendations": [
                    "Urgent neurology referral",
                    "Consider advanced imaging",
                    "Monitor neurological status"
                ]
            }
        }
        
        print("Sample Triage Report Generated:")
        print(f"  Patient ID: {sample_report['patient_id']}")
        print(f"  Priority Level: {sample_report['executive_summary']['priority_level']}")
        print(f"  Confidence: {sample_report['executive_summary']['confidence_score']:.2f}")
        
        print("\nKey Findings:")
        for finding in sample_report['executive_summary']['key_findings']:
            print(f"  • {finding}")
        
        print("\nImmediate Actions:")
        for action in sample_report['executive_summary']['immediate_actions']:
            print(f"  • {action}")
        
        # Save sample report
        output_dir = Path("outputs/demo")
        output_dir.mkdir(parents=True, exist_ok=True)
        
        import json
        report_path = output_dir / "sample_triage_report.json"
        with open(report_path, 'w') as f:
            json.dump(sample_report, f, indent=2)
        
        print(f"\n✓ Sample report saved to: {report_path}")
        
        # Create demo visualizations directory
        viz_dir = output_dir / "visualizations"
        viz_dir.mkdir(exist_ok=True)
        print(f"✓ Visualization directory created: {viz_dir}")
        
        print("✅ Results and reporting demonstration completed")
        time.sleep(2)
    
    def demo_summary(self):
        """Provide demo summary and next steps."""
        demo_duration = datetime.now() - self.demo_start_time
        
        print("\n🎉 DEMO SUMMARY")
        print("="*80)
        print(f"Demo completed in: {demo_duration}")
        print("\nWhat was demonstrated:")
        print("✓ Environment setup and validation")
        print("✓ Synthetic data generation")
        print("✓ Model training workflow")
        print("✓ Inference pipeline components")
        print("✓ Results reporting and visualization")
        
        print("\nNext Steps for Full Implementation:")
        print("1. 📊 Obtain real medical datasets (BraTS, institutional data)")
        print("2. 🔑 Set up API keys for LLM services (OpenAI, Anthropic)")
        print("3. 🚀 Run full model training: python scripts/train_model.py")
        print("4. 🔍 Test inference: python scripts/run_inference.py")
        print("5. 🏥 Deploy in clinical environment with proper approvals")
        
        print("\nImportant Notes:")
        print("⚠️  This is a research/educational demonstration")
        print("⚠️  Clinical deployment requires regulatory approval")
        print("⚠️  Ensure HIPAA/GDPR compliance for real patient data")
        print("⚠️  Human oversight required for all clinical decisions")
        
        print("\n📚 Documentation and Resources:")
        print("• README.md - Project overview and setup")
        print("• configs/config.yaml - Configuration settings")
        print("• scripts/ - Training and inference scripts")
        print("• src/healthcare_triage/ - Core implementation")
        
        print("\n" + "="*80)
        print("HEALTHCARE MULTIMODAL TRIAGE AGENT DEMO COMPLETED")
        print("="*80)


def main():
    """Run the complete demo."""
    try:
        demo = HealthcareTriageDemo()
        demo.run_complete_demo()
    except KeyboardInterrupt:
        print("\n\nDemo interrupted by user")
        sys.exit(0)
    except Exception as e:
        print(f"\nDemo failed with error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
